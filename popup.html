<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>题目保存插件</title>
  <link rel="stylesheet" href="popup.css">
</head>
<body>
  <div class="container">
    <h1>题目保存插件</h1>
    <p>点击下方按钮获取当前页面中的题目数据并保存为JSON格式。</p>
    
    <div class="form-group">
      <label for="savePath">保存位置:</label>
      <input type="text" id="savePath" placeholder="请输入保存路径" value="">
      <button id="browseBtn">浏览</button>
    </div>
    
    <button id="getQuestionsBtn">获取题目数据</button>
    
    <div id="resultArea">
      <p id="questionCount"></p>
      <button id="saveBtn" style="display: none;">保存为JSON</button>
    </div>
  </div>
  
  <script src="popup.js"></script>
</body>
</html>
