document.addEventListener('DOMContentLoaded', function() {
  const getQuestionsBtn = document.getElementById('getQuestionsBtn');
  const saveBtn = document.getElementById('saveBtn');
  const questionCount = document.getElementById('questionCount');
  const savePathInput = document.getElementById('savePath');
  const browseBtn = document.getElementById('browseBtn');
  let questionsData = [];
  
  // 获取题目数据
  getQuestionsBtn.addEventListener('click', function() {
    chrome.tabs.query({ active: true, currentWindow: true }, function(tabs) {
      chrome.tabs.sendMessage(tabs[0].id, { action: 'getQuestions' }, function(response) {
        if (response && response.error) {
          questionCount.textContent = response.error;
          saveBtn.style.display = 'none';
        } else if (response && response.questions) {
          questionsData = response.questions;
          questionCount.textContent = `已获取 ${questionsData.length} 道题目`;
          saveBtn.style.display = 'block';
        } else {
          questionCount.textContent = '未能获取题目数据，请确保在正确的页面上。';
          saveBtn.style.display = 'none';
        }
      });
    });
  });
  
  // 浏览保存位置
  browseBtn.addEventListener('click', function() {
    // 由于Chrome插件的限制，无法直接访问文件系统
    // 这里只是一个示例，实际需要使用Chrome的File System API或下载API
    alert('由于浏览器限制，保存位置需要手动输入完整路径。');
  });
  
  // 保存为JSON
  saveBtn.addEventListener('click', function() {
    if (questionsData.length === 0) {
      alert('没有题目数据可保存！');
      return;
    }
    
    const jsonContent = JSON.stringify(questionsData, null, 2);
    const blob = new Blob([jsonContent], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'questions.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    alert('题目数据已保存为JSON文件。');
  });
  
  // 加载保存的路径
  chrome.storage.sync.get(['savePath'], function(result) {
    if (result.savePath) {
      savePathInput.value = result.savePath;
    }
  });
  
  // 保存路径变化时存储
  savePathInput.addEventListener('change', function() {
    chrome.storage.sync.set({ savePath: savePathInput.value });
  });
});
