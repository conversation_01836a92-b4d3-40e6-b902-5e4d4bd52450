<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>题库测试页面 - 高一数学</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .question-item {
            border: 1px solid #ddd;
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .question-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .question-meta {
            font-size: 12px;
            color: #666;
            margin-bottom: 10px;
        }
        .options {
            margin: 10px 0;
        }
        .option {
            margin: 5px 0;
            padding: 5px;
        }
        .answer {
            margin-top: 10px;
            padding: 10px;
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: #f0f8ff;
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>高一数学题库测试页面</h1>
        <p>这是一个用于测试题库管理系统Chrome插件的示例页面</p>
        <p>学科：数学 | 年级：高一 | 难度：中等</p>
    </div>

    <div class="question-item">
        <div class="question-meta">高一 数学 中等 组卷5次</div>
        <div class="question-title">1. 求解一元二次方程 x² - 5x + 6 = 0 的解</div>
        <div class="options">
            <div class="option">A. x = 2 或 x = 3</div>
            <div class="option">B. x = 1 或 x = 6</div>
            <div class="option">C. x = -2 或 x = -3</div>
            <div class="option">D. x = 0 或 x = 5</div>
        </div>
        <div class="answer">答案：A. 使用因式分解法：(x-2)(x-3)=0，所以 x=2 或 x=3</div>
    </div>

    <div class="question-item">
        <div class="question-meta">高一 数学 简单 组卷12次</div>
        <div class="question-title">2. 计算函数 f(x) = 2x + 3 在 x = 4 时的函数值</div>
        <div class="options">
            <div class="option">A. 8</div>
            <div class="option">B. 11</div>
            <div class="option">C. 14</div>
            <div class="option">D. 17</div>
        </div>
        <div class="answer">答案：B. f(4) = 2×4 + 3 = 8 + 3 = 11</div>
    </div>

    <div class="question-item">
        <div class="question-meta">高一 数学 困难 组卷3次</div>
        <div class="question-title">3. 已知函数 f(x) = ax² + bx + c，若 f(1) = 0，f(2) = 3，f(3) = 8，求 a、b、c 的值</div>
        <div class="options">
            <div class="option">A. a=1, b=-2, c=1</div>
            <div class="option">B. a=1, b=0, c=-1</div>
            <div class="option">C. a=2, b=-3, c=1</div>
            <div class="option">D. a=1, b=-1, c=0</div>
        </div>
        <div class="answer">答案：A. 通过建立方程组求解得到 a=1, b=-2, c=1</div>
    </div>

    <div class="question-item">
        <div class="question-meta">高一 数学 中等 组卷8次</div>
        <div class="question-title">4. 求不等式 2x - 3 > 5 的解集</div>
        <div class="options">
            <div class="option">A. x > 4</div>
            <div class="option">B. x > 1</div>
            <div class="option">C. x < 4</div>
            <div class="option">D. x < 1</div>
        </div>
        <div class="answer">答案：A. 2x - 3 > 5，2x > 8，x > 4</div>
    </div>

    <div class="question-item">
        <div class="question-meta">高一 数学 简单 组卷15次</div>
        <div class="question-title">5. 化简表达式 (x + 2)(x - 2)</div>
        <div class="options">
            <div class="option">A. x² - 4</div>
            <div class="option">B. x² + 4</div>
            <div class="option">C. x² - 2x + 4</div>
            <div class="option">D. x² + 2x - 4</div>
        </div>
        <div class="answer">答案：A. 使用平方差公式：(x + 2)(x - 2) = x² - 4</div>
    </div>

    <div class="question-item">
        <div class="question-meta">高一 数学 困难 组卷2次</div>
        <div class="question-title">6. 已知等差数列 {aₙ} 的首项 a₁ = 3，公差 d = 2，求第10项 a₁₀ 的值</div>
        <div class="options">
            <div class="option">A. 19</div>
            <div class="option">B. 21</div>
            <div class="option">C. 23</div>
            <div class="option">D. 25</div>
        </div>
        <div class="answer">答案：B. 使用等差数列通项公式：a₁₀ = a₁ + 9d = 3 + 9×2 = 21</div>
    </div>

    <script>
        // 模拟动态加载内容
        console.log('测试页面已加载，包含6道数学题目');
        
        // 添加一些交互功能
        document.querySelectorAll('.question-item').forEach((item, index) => {
            item.addEventListener('click', () => {
                console.log(`点击了第${index + 1}道题目`);
            });
        });
    </script>
</body>
</html>
