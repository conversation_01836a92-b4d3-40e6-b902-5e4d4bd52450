!function(t){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=t();else if("function"==typeof define&&define.amd)define([],t);else{("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).superagent=t()}}((function(){var t={exports:{}};function e(t){if(t)return function(t){for(var r in e.prototype)t[r]=e.prototype[r];return t}(t)}t.exports=e,e.prototype.on=e.prototype.addEventListener=function(t,e){return this._callbacks=this._callbacks||{},(this._callbacks["$"+t]=this._callbacks["$"+t]||[]).push(e),this},e.prototype.once=function(t,e){function r(){this.off(t,r),e.apply(this,arguments)}return r.fn=e,this.on(t,r),this},e.prototype.off=e.prototype.removeListener=e.prototype.removeAllListeners=e.prototype.removeEventListener=function(t,e){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var r,o=this._callbacks["$"+t];if(!o)return this;if(1==arguments.length)return delete this._callbacks["$"+t],this;for(var n=0;n<o.length;n++)if((r=o[n])===e||r.fn===e){o.splice(n,1);break}return 0===o.length&&delete this._callbacks["$"+t],this},e.prototype.emit=function(t){this._callbacks=this._callbacks||{};for(var e=new Array(arguments.length-1),r=this._callbacks["$"+t],o=1;o<arguments.length;o++)e[o-1]=arguments[o];if(r){o=0;for(var n=(r=r.slice(0)).length;o<n;++o)r[o].apply(this,e)}return this},e.prototype.listeners=function(t){return this._callbacks=this._callbacks||{},this._callbacks["$"+t]||[]},e.prototype.hasListeners=function(t){return!!this.listeners(t).length},t=t.exports;var r;r=a,a.default=a,a.stable=p,a.stableStringify=p;var o=[],n=[];function i(){return{depthLimit:Number.MAX_SAFE_INTEGER,edgesLimit:Number.MAX_SAFE_INTEGER}}function a(t,e,r,a){var u;void 0===a&&(a=i()),function t(e,r,o,n,i,a,u){var p;if(a+=1,"object"==typeof e&&null!==e){for(p=0;p<n.length;p++)if(n[p]===e)return void s("[Circular]",e,r,i);if(void 0!==u.depthLimit&&a>u.depthLimit)return void s("[...]",e,r,i);if(void 0!==u.edgesLimit&&o+1>u.edgesLimit)return void s("[...]",e,r,i);if(n.push(e),Array.isArray(e))for(p=0;p<e.length;p++)t(e[p],p,p,n,e,a,u);else{var c=Object.keys(e);for(p=0;p<c.length;p++){var l=c[p];t(e[l],l,p,n,e,a,u)}}n.pop()}}(t,"",0,[],void 0,0,a);try{u=0===n.length?JSON.stringify(t,e,r):JSON.stringify(t,c(e),r)}catch(l){return JSON.stringify("[unable to serialize, circular reference is too complex to analyze]")}finally{for(;0!==o.length;){var p=o.pop();4===p.length?Object.defineProperty(p[0],p[1],p[3]):p[0][p[1]]=p[2]}}return u}function s(t,e,r,i){var a=Object.getOwnPropertyDescriptor(i,r);void 0!==a.get?a.configurable?(Object.defineProperty(i,r,{value:t}),o.push([i,r,e,a])):n.push([e,r,t]):(i[r]=t,o.push([i,r,e]))}function u(t,e){return t<e?-1:t>e?1:0}function p(t,e,r,a){void 0===a&&(a=i());var p,l=function t(e,r,n,i,a,p,c){var l;if(p+=1,"object"==typeof e&&null!==e){for(l=0;l<i.length;l++)if(i[l]===e)return void s("[Circular]",e,r,a);try{if("function"==typeof e.toJSON)return}catch(d){return}if(void 0!==c.depthLimit&&p>c.depthLimit)return void s("[...]",e,r,a);if(void 0!==c.edgesLimit&&n+1>c.edgesLimit)return void s("[...]",e,r,a);if(i.push(e),Array.isArray(e))for(l=0;l<e.length;l++)t(e[l],l,l,i,e,p,c);else{var f={},y=Object.keys(e).sort(u);for(l=0;l<y.length;l++){var h=y[l];t(e[h],h,l,i,e,p,c),f[h]=e[h]}if(void 0===a)return f;o.push([a,r,e]),a[r]=f}i.pop()}}(t,"",0,[],void 0,0,a)||t;try{p=0===n.length?JSON.stringify(l,e,r):JSON.stringify(l,c(e),r)}catch(y){return JSON.stringify("[unable to serialize, circular reference is too complex to analyze]")}finally{for(;0!==o.length;){var f=o.pop();4===f.length?Object.defineProperty(f[0],f[1],f[3]):f[0][f[1]]=f[2]}}return p}function c(t){return t=void 0!==t?t:function(t,e){return e},function(e,r){if(n.length>0)for(var o=0;o<n.length;o++){var i=n[o];if(i[1]===e&&i[0]===r){r=i[2],n.splice(o,1);break}}return t.call(this,e,r)}}var l="undefined"!=typeof Symbol&&Symbol,f={foo:{}},y=Object,h=Array.prototype.slice,d=Object.prototype.toString,m=Function.prototype.bind||function(t){var e=this;if("function"!=typeof e||"[object Function]"!==d.call(e))throw new TypeError("Function.prototype.bind called on incompatible "+e);for(var r,o=h.call(arguments,1),n=Math.max(0,e.length-o.length),i=[],a=0;a<n;a++)i.push("$"+a);if(r=Function("binder","return function ("+i.join(",")+"){ return binder.apply(this,arguments); }")((function(){if(this instanceof r){var n=e.apply(this,o.concat(h.call(arguments)));return Object(n)===n?n:this}return e.apply(t,o.concat(h.call(arguments)))})),e.prototype){var s=function(){};s.prototype=e.prototype,r.prototype=new s,s.prototype=null}return r},b=m.call(Function.call,Object.prototype.hasOwnProperty),g=SyntaxError,v=Function,w=TypeError,_=function(t){try{return v('"use strict"; return ('+t+").constructor;")()}catch(e){}},S=Object.getOwnPropertyDescriptor;if(S)try{S({},"")}catch(ir){S=null}var A=function(){throw new w},E=S?function(){try{return A}catch(t){try{return S(arguments,"callee").get}catch(e){return A}}}():A,O="function"==typeof l&&"function"==typeof Symbol&&"symbol"==typeof l("foo")&&"symbol"==typeof Symbol("bar")&&function(){if("function"!=typeof Symbol||"function"!=typeof Object.getOwnPropertySymbols)return!1;if("symbol"==typeof Symbol.iterator)return!0;var t={},e=Symbol("test"),r=Object(e);if("string"==typeof e)return!1;if("[object Symbol]"!==Object.prototype.toString.call(e))return!1;if("[object Symbol]"!==Object.prototype.toString.call(r))return!1;for(e in t[e]=42,t)return!1;if("function"==typeof Object.keys&&0!==Object.keys(t).length)return!1;if("function"==typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(t).length)return!1;var o=Object.getOwnPropertySymbols(t);if(1!==o.length||o[0]!==e)return!1;if(!Object.prototype.propertyIsEnumerable.call(t,e))return!1;if("function"==typeof Object.getOwnPropertyDescriptor){var n=Object.getOwnPropertyDescriptor(t,e);if(42!==n.value||!0!==n.enumerable)return!1}return!0}(),j={__proto__:f}.foo===f.foo&&!({__proto__:null}instanceof y),T=Object.getPrototypeOf||(j?function(t){return t.__proto__}:null),P={},x="undefined"!=typeof Uint8Array&&T?T(Uint8Array):void 0,k={"%AggregateError%":"undefined"==typeof AggregateError?void 0:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"==typeof ArrayBuffer?void 0:ArrayBuffer,"%ArrayIteratorPrototype%":O&&T?T([][Symbol.iterator]()):void 0,"%AsyncFromSyncIteratorPrototype%":void 0,"%AsyncFunction%":P,"%AsyncGenerator%":P,"%AsyncGeneratorFunction%":P,"%AsyncIteratorPrototype%":P,"%Atomics%":"undefined"==typeof Atomics?void 0:Atomics,"%BigInt%":"undefined"==typeof BigInt?void 0:BigInt,"%BigInt64Array%":"undefined"==typeof BigInt64Array?void 0:BigInt64Array,"%BigUint64Array%":"undefined"==typeof BigUint64Array?void 0:BigUint64Array,"%Boolean%":Boolean,"%DataView%":"undefined"==typeof DataView?void 0:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":Error,"%eval%":eval,"%EvalError%":EvalError,"%Float32Array%":"undefined"==typeof Float32Array?void 0:Float32Array,"%Float64Array%":"undefined"==typeof Float64Array?void 0:Float64Array,"%FinalizationRegistry%":"undefined"==typeof FinalizationRegistry?void 0:FinalizationRegistry,"%Function%":v,"%GeneratorFunction%":P,"%Int8Array%":"undefined"==typeof Int8Array?void 0:Int8Array,"%Int16Array%":"undefined"==typeof Int16Array?void 0:Int16Array,"%Int32Array%":"undefined"==typeof Int32Array?void 0:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":O&&T?T(T([][Symbol.iterator]())):void 0,"%JSON%":"object"==typeof JSON?JSON:void 0,"%Map%":"undefined"==typeof Map?void 0:Map,"%MapIteratorPrototype%":"undefined"!=typeof Map&&O&&T?T((new Map)[Symbol.iterator]()):void 0,"%Math%":Math,"%Number%":Number,"%Object%":Object,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"==typeof Promise?void 0:Promise,"%Proxy%":"undefined"==typeof Proxy?void 0:Proxy,"%RangeError%":RangeError,"%ReferenceError%":ReferenceError,"%Reflect%":"undefined"==typeof Reflect?void 0:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"==typeof Set?void 0:Set,"%SetIteratorPrototype%":"undefined"!=typeof Set&&O&&T?T((new Set)[Symbol.iterator]()):void 0,"%SharedArrayBuffer%":"undefined"==typeof SharedArrayBuffer?void 0:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":O&&T?T(""[Symbol.iterator]()):void 0,"%Symbol%":O?Symbol:void 0,"%SyntaxError%":g,"%ThrowTypeError%":E,"%TypedArray%":x,"%TypeError%":w,"%Uint8Array%":"undefined"==typeof Uint8Array?void 0:Uint8Array,"%Uint8ClampedArray%":"undefined"==typeof Uint8ClampedArray?void 0:Uint8ClampedArray,"%Uint16Array%":"undefined"==typeof Uint16Array?void 0:Uint16Array,"%Uint32Array%":"undefined"==typeof Uint32Array?void 0:Uint32Array,"%URIError%":URIError,"%WeakMap%":"undefined"==typeof WeakMap?void 0:WeakMap,"%WeakRef%":"undefined"==typeof WeakRef?void 0:WeakRef,"%WeakSet%":"undefined"==typeof WeakSet?void 0:WeakSet};if(T)try{null.error}catch(ir){var R=T(T(ir));k["%Error.prototype%"]=R}var I,C={"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},N=m.call(Function.call,Array.prototype.concat),F=m.call(Function.apply,Array.prototype.splice),D=m.call(Function.call,String.prototype.replace),U=m.call(Function.call,String.prototype.slice),M=m.call(Function.call,RegExp.prototype.exec),L=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,q=/\\(\\)?/g,B=function(t,e){var r,o=t;if(b(C,o)&&(o="%"+(r=C[o])[0]+"%"),b(k,o)){var n=k[o];if(n===P&&(n=function t(e){var r;if("%AsyncFunction%"===e)r=_("async function () {}");else if("%GeneratorFunction%"===e)r=_("function* () {}");else if("%AsyncGeneratorFunction%"===e)r=_("async function* () {}");else if("%AsyncGenerator%"===e){var o=t("%AsyncGeneratorFunction%");o&&(r=o.prototype)}else if("%AsyncIteratorPrototype%"===e){var n=t("%AsyncGenerator%");n&&T&&(r=T(n.prototype))}return k[e]=r,r}(o)),void 0===n&&!e)throw new w("intrinsic "+t+" exists, but is not available. Please file an issue!");return{alias:r,name:o,value:n}}throw new g("intrinsic "+t+" does not exist!")},W=function(t,e){if("string"!=typeof t||0===t.length)throw new w("intrinsic name must be a non-empty string");if(arguments.length>1&&"boolean"!=typeof e)throw new w('"allowMissing" argument must be a boolean');if(null===M(/^%?[^%]*%?$/,t))throw new g("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var r=function(t){var e=U(t,0,1),r=U(t,-1);if("%"===e&&"%"!==r)throw new g("invalid intrinsic syntax, expected closing `%`");if("%"===r&&"%"!==e)throw new g("invalid intrinsic syntax, expected opening `%`");var o=[];return D(t,L,(function(t,e,r,n){o[o.length]=r?D(n,q,"$1"):e||t})),o}(t),o=r.length>0?r[0]:"",n=B("%"+o+"%",e),i=n.name,a=n.value,s=!1,u=n.alias;u&&(o=u[0],F(r,N([0,1],u)));for(var p=1,c=!0;p<r.length;p+=1){var l=r[p],f=U(l,0,1),y=U(l,-1);if(('"'===f||"'"===f||"`"===f||'"'===y||"'"===y||"`"===y)&&f!==y)throw new g("property names with quotes must have matching quotes");if("constructor"!==l&&c||(s=!0),b(k,i="%"+(o+="."+l)+"%"))a=k[i];else if(null!=a){if(!(l in a)){if(!e)throw new w("base intrinsic for "+t+" exists, but the property is not available.");return}if(S&&p+1>=r.length){var h=S(a,l);a=(c=!!h)&&"get"in h&&!("originalValue"in h.get)?h.get:a[l]}else c=b(a,l),a=a[l];c&&!s&&(k[i]=a)}}return a},$=W("%Function.prototype.apply%"),H=W("%Function.prototype.call%"),z=W("%Reflect.apply%",!0)||m.call(H,$),G=W("%Object.getOwnPropertyDescriptor%",!0),J=W("%Object.defineProperty%",!0),V=W("%Math.max%");if(J)try{J({},"a",{value:1})}catch(ir){J=null}I=function(t){var e=z(m,H,arguments);return G&&J&&G(e,"length").configurable&&J(e,"length",{value:1+V(0,t.length-(arguments.length-1))}),e};var Q=function(){return z(m,$,arguments)};J?J(I,"apply",{value:Q}):I.apply=Q;var X=I(W("String.prototype.indexOf")),K=function(t,e){var r=W(t,!!e);return"function"==typeof r&&X(t,".prototype.")>-1?I(r):r},Y={},Z="function"==typeof Map&&Map.prototype,tt=Object.getOwnPropertyDescriptor&&Z?Object.getOwnPropertyDescriptor(Map.prototype,"size"):null,et=Z&&tt&&"function"==typeof tt.get?tt.get:null,rt=Z&&Map.prototype.forEach,ot="function"==typeof Set&&Set.prototype,nt=Object.getOwnPropertyDescriptor&&ot?Object.getOwnPropertyDescriptor(Set.prototype,"size"):null,it=ot&&nt&&"function"==typeof nt.get?nt.get:null,at=ot&&Set.prototype.forEach,st="function"==typeof WeakMap&&WeakMap.prototype?WeakMap.prototype.has:null,ut="function"==typeof WeakSet&&WeakSet.prototype?WeakSet.prototype.has:null,pt="function"==typeof WeakRef&&WeakRef.prototype?WeakRef.prototype.deref:null,ct=Boolean.prototype.valueOf,lt=Object.prototype.toString,ft=Function.prototype.toString,yt=String.prototype.match,ht=String.prototype.slice,dt=String.prototype.replace,mt=String.prototype.toUpperCase,bt=String.prototype.toLowerCase,gt=RegExp.prototype.test,vt=Array.prototype.concat,wt=Array.prototype.join,_t=Array.prototype.slice,St=Math.floor,At="function"==typeof BigInt?BigInt.prototype.valueOf:null,Et=Object.getOwnPropertySymbols,Ot="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?Symbol.prototype.toString:null,jt="function"==typeof Symbol&&"object"==typeof Symbol.iterator,Tt="function"==typeof Symbol&&Symbol.toStringTag&&(Symbol.toStringTag,1)?Symbol.toStringTag:null,Pt=Object.prototype.propertyIsEnumerable,xt=("function"==typeof Reflect?Reflect.getPrototypeOf:Object.getPrototypeOf)||([].__proto__===Array.prototype?function(t){return t.__proto__}:null);function kt(t,e){if(t===1/0||t===-1/0||t!=t||t&&t>-1e3&&t<1e3||gt.call(/e/,e))return e;var r=/[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;if("number"==typeof t){var o=t<0?-St(-t):St(t);if(o!==t){var n=String(o),i=ht.call(e,n.length+1);return dt.call(n,r,"$&_")+"."+dt.call(dt.call(i,/([0-9]{3})/g,"$&_"),/_$/,"")}}return dt.call(e,r,"$&_")}var Rt=Y.custom,It=Ut(Rt)?Rt:null;function Ct(t,e,r){var o="double"===(r.quoteStyle||e)?'"':"'";return o+t+o}function Nt(t){return dt.call(String(t),/"/g,"&quot;")}function Ft(t){return!("[object Array]"!==qt(t)||Tt&&"object"==typeof t&&Tt in t)}function Dt(t){return!("[object RegExp]"!==qt(t)||Tt&&"object"==typeof t&&Tt in t)}function Ut(t){if(jt)return t&&"object"==typeof t&&t instanceof Symbol;if("symbol"==typeof t)return!0;if(!t||"object"!=typeof t||!Ot)return!1;try{return Ot.call(t),!0}catch(ir){}return!1}var Mt=Object.prototype.hasOwnProperty||function(t){return t in this};function Lt(t,e){return Mt.call(t,e)}function qt(t){return lt.call(t)}function Bt(t,e){if(t.indexOf)return t.indexOf(e);for(var r=0,o=t.length;r<o;r++)if(t[r]===e)return r;return-1}function Wt(t){var e=t.charCodeAt(0),r={8:"b",9:"t",10:"n",12:"f",13:"r"}[e];return r?"\\"+r:"\\x"+(e<16?"0":"")+mt.call(e.toString(16))}function $t(t){return"Object("+t+")"}function Ht(t){return t+" { ? }"}function zt(t,e,r,o){return t+" ("+e+") {"+(o?Gt(r,o):wt.call(r,", "))+"}"}function Gt(t,e){if(0===t.length)return"";var r="\n"+e.prev+e.base;return r+wt.call(t,","+r)+"\n"+e.prev}function Jt(t,e){var r=Ft(t),o=[];if(r){o.length=t.length;for(var n=0;n<t.length;n++)o[n]=Lt(t,n)?e(t[n],t):""}var i,a="function"==typeof Et?Et(t):[];if(jt){i={};for(var s=0;s<a.length;s++)i["$"+a[s]]=a[s]}for(var u in t)Lt(t,u)&&(r&&String(Number(u))===u&&u<t.length||jt&&i["$"+u]instanceof Symbol||(gt.call(/[^\w$]/,u)?o.push(e(u,t)+": "+e(t[u],t)):o.push(u+": "+e(t[u],t))));if("function"==typeof Et)for(var p=0;p<a.length;p++)Pt.call(t,a[p])&&o.push("["+e(a[p])+"]: "+e(t[a[p]],t));return o}var Vt=W("%TypeError%"),Qt=W("%WeakMap%",!0),Xt=W("%Map%",!0),Kt=K("WeakMap.prototype.get",!0),Yt=K("WeakMap.prototype.set",!0),Zt=K("WeakMap.prototype.has",!0),te=K("Map.prototype.get",!0),ee=K("Map.prototype.set",!0),re=K("Map.prototype.has",!0),oe=function(t,e){for(var r,o=t;null!==(r=o.next);o=r)if(r.key===e)return o.next=r.next,r.next=t.next,t.next=r,r},ne=function(){var t,e,r,o={assert:function(t){if(!o.has(t))throw new Vt("Side channel does not contain "+function t(e,r,o,n){var i=r||{};if(Lt(i,"quoteStyle")&&"single"!==i.quoteStyle&&"double"!==i.quoteStyle)throw new TypeError('option "quoteStyle" must be "single" or "double"');if(Lt(i,"maxStringLength")&&("number"==typeof i.maxStringLength?i.maxStringLength<0&&i.maxStringLength!==1/0:null!==i.maxStringLength))throw new TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');var a=!Lt(i,"customInspect")||i.customInspect;if("boolean"!=typeof a&&"symbol"!==a)throw new TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");if(Lt(i,"indent")&&null!==i.indent&&"\t"!==i.indent&&!(parseInt(i.indent,10)===i.indent&&i.indent>0))throw new TypeError('option "indent" must be "\\t", an integer > 0, or `null`');if(Lt(i,"numericSeparator")&&"boolean"!=typeof i.numericSeparator)throw new TypeError('option "numericSeparator", if provided, must be `true` or `false`');var s=i.numericSeparator;if(void 0===e)return"undefined";if(null===e)return"null";if("boolean"==typeof e)return e?"true":"false";if("string"==typeof e)return function t(e,r){if(e.length>r.maxStringLength){var o=e.length-r.maxStringLength,n="... "+o+" more character"+(o>1?"s":"");return t(ht.call(e,0,r.maxStringLength),r)+n}return Ct(dt.call(dt.call(e,/(['\\])/g,"\\$1"),/[\x00-\x1f]/g,Wt),"single",r)}(e,i);if("number"==typeof e){if(0===e)return 1/0/e>0?"0":"-0";var u=String(e);return s?kt(e,u):u}if("bigint"==typeof e){var p=String(e)+"n";return s?kt(e,p):p}var c=void 0===i.depth?5:i.depth;if(void 0===o&&(o=0),o>=c&&c>0&&"object"==typeof e)return Ft(e)?"[Array]":"[Object]";var l,f=function(t,e){var r;if("\t"===t.indent)r="\t";else{if(!("number"==typeof t.indent&&t.indent>0))return null;r=wt.call(Array(t.indent+1)," ")}return{base:r,prev:wt.call(Array(e+1),r)}}(i,o);if(void 0===n)n=[];else if(Bt(n,e)>=0)return"[Circular]";function y(e,r,a){if(r&&(n=_t.call(n)).push(r),a){var s={depth:i.depth};return Lt(i,"quoteStyle")&&(s.quoteStyle=i.quoteStyle),t(e,s,o+1,n)}return t(e,i,o+1,n)}if("function"==typeof e&&!Dt(e)){var h=function(t){if(t.name)return t.name;var e=yt.call(ft.call(t),/^function\s*([\w$]+)/);return e?e[1]:null}(e),d=Jt(e,y);return"[Function"+(h?": "+h:" (anonymous)")+"]"+(d.length>0?" { "+wt.call(d,", ")+" }":"")}if(Ut(e)){var m=jt?dt.call(String(e),/^(Symbol\(.*\))_[^)]*$/,"$1"):Ot.call(e);return"object"!=typeof e||jt?m:$t(m)}if((l=e)&&"object"==typeof l&&("undefined"!=typeof HTMLElement&&l instanceof HTMLElement||"string"==typeof l.nodeName&&"function"==typeof l.getAttribute)){for(var b="<"+bt.call(String(e.nodeName)),g=e.attributes||[],v=0;v<g.length;v++)b+=" "+g[v].name+"="+Ct(Nt(g[v].value),"double",i);return b+=">",e.childNodes&&e.childNodes.length&&(b+="..."),b+"</"+bt.call(String(e.nodeName))+">"}if(Ft(e)){if(0===e.length)return"[]";var w=Jt(e,y);return f&&!function(t){for(var e=0;e<t.length;e++)if(Bt(t[e],"\n")>=0)return!1;return!0}(w)?"["+Gt(w,f)+"]":"[ "+wt.call(w,", ")+" ]"}if(function(t){return!("[object Error]"!==qt(t)||Tt&&"object"==typeof t&&Tt in t)}(e)){var _=Jt(e,y);return"cause"in Error.prototype||!("cause"in e)||Pt.call(e,"cause")?0===_.length?"["+String(e)+"]":"{ ["+String(e)+"] "+wt.call(_,", ")+" }":"{ ["+String(e)+"] "+wt.call(vt.call("[cause]: "+y(e.cause),_),", ")+" }"}if("object"==typeof e&&a){if(It&&"function"==typeof e[It]&&Y)return Y(e,{depth:c-o});if("symbol"!==a&&"function"==typeof e.inspect)return e.inspect()}if(function(t){if(!et||!t||"object"!=typeof t)return!1;try{et.call(t);try{it.call(t)}catch(b){return!0}return t instanceof Map}catch(ir){}return!1}(e)){var S=[];return rt&&rt.call(e,(function(t,r){S.push(y(r,e,!0)+" => "+y(t,e))})),zt("Map",et.call(e),S,f)}if(function(t){if(!it||!t||"object"!=typeof t)return!1;try{it.call(t);try{et.call(t)}catch(e){return!0}return t instanceof Set}catch(ir){}return!1}(e)){var A=[];return at&&at.call(e,(function(t){A.push(y(t,e))})),zt("Set",it.call(e),A,f)}if(function(t){if(!st||!t||"object"!=typeof t)return!1;try{st.call(t,st);try{ut.call(t,ut)}catch(b){return!0}return t instanceof WeakMap}catch(ir){}return!1}(e))return Ht("WeakMap");if(function(t){if(!ut||!t||"object"!=typeof t)return!1;try{ut.call(t,ut);try{st.call(t,st)}catch(b){return!0}return t instanceof WeakSet}catch(ir){}return!1}(e))return Ht("WeakSet");if(function(t){if(!pt||!t||"object"!=typeof t)return!1;try{return pt.call(t),!0}catch(ir){}return!1}(e))return Ht("WeakRef");if(function(t){return!("[object Number]"!==qt(t)||Tt&&"object"==typeof t&&Tt in t)}(e))return $t(y(Number(e)));if(function(t){if(!t||"object"!=typeof t||!At)return!1;try{return At.call(t),!0}catch(ir){}return!1}(e))return $t(y(At.call(e)));if(function(t){return!("[object Boolean]"!==qt(t)||Tt&&"object"==typeof t&&Tt in t)}(e))return $t(ct.call(e));if(function(t){return!("[object String]"!==qt(t)||Tt&&"object"==typeof t&&Tt in t)}(e))return $t(y(String(e)));if(!function(t){return!("[object Date]"!==qt(t)||Tt&&"object"==typeof t&&Tt in t)}(e)&&!Dt(e)){var E=Jt(e,y),O=xt?xt(e)===Object.prototype:e instanceof Object||e.constructor===Object,j=e instanceof Object?"":"null prototype",T=!O&&Tt&&Object(e)===e&&Tt in e?ht.call(qt(e),8,-1):j?"Object":"",P=(O||"function"!=typeof e.constructor?"":e.constructor.name?e.constructor.name+" ":"")+(T||j?"["+wt.call(vt.call([],T||[],j||[]),": ")+"] ":"");return 0===E.length?P+"{}":f?P+"{"+Gt(E,f)+"}":P+"{ "+wt.call(E,", ")+" }"}return String(e)}(t))},get:function(o){if(Qt&&o&&("object"==typeof o||"function"==typeof o)){if(t)return Kt(t,o)}else if(Xt){if(e)return te(e,o)}else if(r)return function(t,e){var r=oe(t,e);return r&&r.value}(r,o)},has:function(o){if(Qt&&o&&("object"==typeof o||"function"==typeof o)){if(t)return Zt(t,o)}else if(Xt){if(e)return re(e,o)}else if(r)return function(t,e){return!!oe(t,e)}(r,o);return!1},set:function(o,n){Qt&&o&&("object"==typeof o||"function"==typeof o)?(t||(t=new Qt),Yt(t,o,n)):Xt?(e||(e=new Xt),ee(e,o,n)):(r||(r={key:{},next:null}),function(t,e,r){var o=oe(t,e);o?o.value=r:t.next={key:e,next:t.next,value:r}}(r,o,n))}};return o},ie=String.prototype.replace,ae=/%20/g,se={default:"RFC3986",formatters:{RFC1738:function(t){return ie.call(t,ae,"+")},RFC3986:function(t){return String(t)}},RFC1738:"RFC1738",RFC3986:"RFC3986"},ue=Object.prototype.hasOwnProperty,pe=Array.isArray,ce=function(){for(var t=[],e=0;e<256;++e)t.push("%"+((e<16?"0":"")+e.toString(16)).toUpperCase());return t}(),le={combine:function(t,e){return[].concat(t,e)},compact:function(t){for(var e=[{obj:{o:t},prop:"o"}],r=[],o=0;o<e.length;++o)for(var n=e[o],i=n.obj[n.prop],a=Object.keys(i),s=0;s<a.length;++s){var u=a[s],p=i[u];"object"==typeof p&&null!==p&&-1===r.indexOf(p)&&(e.push({obj:i,prop:u}),r.push(p))}return function(t){for(;t.length>1;){var e=t.pop(),r=e.obj[e.prop];if(pe(r)){for(var o=[],n=0;n<r.length;++n)void 0!==r[n]&&o.push(r[n]);e.obj[e.prop]=o}}}(e),t},decode:function(t,e,r){var o=t.replace(/\+/g," ");if("iso-8859-1"===r)return o.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(o)}catch(ir){return o}},encode:function(t,e,r,o,n){if(0===t.length)return t;var i=t;if("symbol"==typeof t?i=Symbol.prototype.toString.call(t):"string"!=typeof t&&(i=String(t)),"iso-8859-1"===r)return escape(i).replace(/%u[0-9a-f]{4}/gi,(function(t){return"%26%23"+parseInt(t.slice(2),16)+"%3B"}));for(var a="",s=0;s<i.length;++s){var u=i.charCodeAt(s);45===u||46===u||95===u||126===u||u>=48&&u<=57||u>=65&&u<=90||u>=97&&u<=122||n===se.RFC1738&&(40===u||41===u)?a+=i.charAt(s):u<128?a+=ce[u]:u<2048?a+=ce[192|u>>6]+ce[128|63&u]:u<55296||u>=57344?a+=ce[224|u>>12]+ce[128|u>>6&63]+ce[128|63&u]:(s+=1,u=65536+((1023&u)<<10|1023&i.charCodeAt(s)),a+=ce[240|u>>18]+ce[128|u>>12&63]+ce[128|u>>6&63]+ce[128|63&u])}return a},isBuffer:function(t){return!(!t||"object"!=typeof t||!(t.constructor&&t.constructor.isBuffer&&t.constructor.isBuffer(t)))},isRegExp:function(t){return"[object RegExp]"===Object.prototype.toString.call(t)},maybeMap:function(t,e){if(pe(t)){for(var r=[],o=0;o<t.length;o+=1)r.push(e(t[o]));return r}return e(t)},merge:function t(e,r,o){if(!r)return e;if("object"!=typeof r){if(pe(e))e.push(r);else{if(!e||"object"!=typeof e)return[e,r];(o&&(o.plainObjects||o.allowPrototypes)||!ue.call(Object.prototype,r))&&(e[r]=!0)}return e}if(!e||"object"!=typeof e)return[e].concat(r);var n=e;return pe(e)&&!pe(r)&&(n=function(t,e){for(var r=e&&e.plainObjects?Object.create(null):{},o=0;o<t.length;++o)void 0!==t[o]&&(r[o]=t[o]);return r}(e,o)),pe(e)&&pe(r)?(r.forEach((function(r,n){if(ue.call(e,n)){var i=e[n];i&&"object"==typeof i&&r&&"object"==typeof r?e[n]=t(i,r,o):e.push(r)}else e[n]=r})),e):Object.keys(r).reduce((function(e,n){var i=r[n];return ue.call(e,n)?e[n]=t(e[n],i,o):e[n]=i,e}),n)}},fe=Object.prototype.hasOwnProperty,ye={brackets:function(t){return t+"[]"},comma:"comma",indices:function(t,e){return t+"["+e+"]"},repeat:function(t){return t}},he=Array.isArray,de=Array.prototype.push,me=function(t,e){de.apply(t,he(e)?e:[e])},be=Date.prototype.toISOString,ge=se.default,ve={addQueryPrefix:!1,allowDots:!1,charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encoder:le.encode,encodeValuesOnly:!1,format:ge,formatter:se.formatters[ge],indices:!1,serializeDate:function(t){return be.call(t)},skipNulls:!1,strictNullHandling:!1},we={},_e=function t(e,r,o,n,i,a,s,u,p,c,l,f,y,h,d,m){for(var b,g=e,v=m,w=0,_=!1;void 0!==(v=v.get(we))&&!_;){var S=v.get(e);if(w+=1,void 0!==S){if(S===w)throw new RangeError("Cyclic object value");_=!0}void 0===v.get(we)&&(w=0)}if("function"==typeof u?g=u(r,g):g instanceof Date?g=l(g):"comma"===o&&he(g)&&(g=le.maybeMap(g,(function(t){return t instanceof Date?l(t):t}))),null===g){if(i)return s&&!h?s(r,ve.encoder,d,"key",f):r;g=""}if("string"==typeof(b=g)||"number"==typeof b||"boolean"==typeof b||"symbol"==typeof b||"bigint"==typeof b||le.isBuffer(g))return s?[y(h?r:s(r,ve.encoder,d,"key",f))+"="+y(s(g,ve.encoder,d,"value",f))]:[y(r)+"="+y(String(g))];var A,E=[];if(void 0===g)return E;if("comma"===o&&he(g))h&&s&&(g=le.maybeMap(g,s)),A=[{value:g.length>0?g.join(",")||null:void 0}];else if(he(u))A=u;else{var O=Object.keys(g);A=p?O.sort(p):O}for(var j=n&&he(g)&&1===g.length?r+"[]":r,T=0;T<A.length;++T){var P=A[T],x="object"==typeof P&&void 0!==P.value?P.value:g[P];if(!a||null!==x){var k=he(g)?"function"==typeof o?o(j,P):j:j+(c?"."+P:"["+P+"]");m.set(e,w);var R=ne();R.set(we,m),me(E,t(x,k,o,n,i,a,"comma"===o&&h&&he(g)?null:s,u,p,c,l,f,y,h,d,R))}}return E},Se=(Object.prototype.hasOwnProperty,Array.isArray,{stringify:function(t,e){var r,o=t,n=function(t){if(!t)return ve;if(null!==t.encoder&&void 0!==t.encoder&&"function"!=typeof t.encoder)throw new TypeError("Encoder has to be a function.");var e=t.charset||ve.charset;if(void 0!==t.charset&&"utf-8"!==t.charset&&"iso-8859-1"!==t.charset)throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var r=se.default;if(void 0!==t.format){if(!fe.call(se.formatters,t.format))throw new TypeError("Unknown format option provided.");r=t.format}var o=se.formatters[r],n=ve.filter;return("function"==typeof t.filter||he(t.filter))&&(n=t.filter),{addQueryPrefix:"boolean"==typeof t.addQueryPrefix?t.addQueryPrefix:ve.addQueryPrefix,allowDots:void 0===t.allowDots?ve.allowDots:!!t.allowDots,charset:e,charsetSentinel:"boolean"==typeof t.charsetSentinel?t.charsetSentinel:ve.charsetSentinel,delimiter:void 0===t.delimiter?ve.delimiter:t.delimiter,encode:"boolean"==typeof t.encode?t.encode:ve.encode,encoder:"function"==typeof t.encoder?t.encoder:ve.encoder,encodeValuesOnly:"boolean"==typeof t.encodeValuesOnly?t.encodeValuesOnly:ve.encodeValuesOnly,filter:n,format:r,formatter:o,serializeDate:"function"==typeof t.serializeDate?t.serializeDate:ve.serializeDate,skipNulls:"boolean"==typeof t.skipNulls?t.skipNulls:ve.skipNulls,sort:"function"==typeof t.sort?t.sort:null,strictNullHandling:"boolean"==typeof t.strictNullHandling?t.strictNullHandling:ve.strictNullHandling}}(e);"function"==typeof n.filter?o=(0,n.filter)("",o):he(n.filter)&&(r=n.filter);var i,a=[];if("object"!=typeof o||null===o)return"";i=e&&e.arrayFormat in ye?e.arrayFormat:e&&"indices"in e?e.indices?"indices":"repeat":"indices";var s=ye[i];if(e&&"commaRoundTrip"in e&&"boolean"!=typeof e.commaRoundTrip)throw new TypeError("`commaRoundTrip` must be a boolean, or absent");var u="comma"===s&&e&&e.commaRoundTrip;r||(r=Object.keys(o)),n.sort&&r.sort(n.sort);for(var p=ne(),c=0;c<r.length;++c){var l=r[c];n.skipNulls&&null===o[l]||me(a,_e(o[l],l,s,u,n.strictNullHandling,n.skipNulls,n.encode?n.encoder:null,n.filter,n.sort,n.allowDots,n.serializeDate,n.format,n.formatter,n.encodeValuesOnly,n.charset,p))}var f=a.join(n.delimiter),y=!0===n.addQueryPrefix?"?":"";return n.charsetSentinel&&("iso-8859-1"===n.charset?y+="utf8=%26%2310003%3B&":y+="utf8=%E2%9C%93&"),f.length>0?y+f:""}}),Ae={type:t=>t.split(/ *; */).shift(),params:t=>{const e={};for(const r of t.split(/ *; */)){const t=r.split(/ *= */),o=t.shift(),n=t.shift();o&&n&&(e[o]=n)}return e},parseLinks:t=>{const e={};for(const r of t.split(/ *, */)){const t=r.split(/ *; */),o=t[0].slice(1,-1);e[t[1].split(/ *= */)[1].slice(1,-1)]=o}return e},isObject:t=>null!==t&&"object"==typeof t};Ae.hasOwn=Object.hasOwn||function(t,e){if(null==t)throw new TypeError("Cannot convert undefined or null to object");return Object.prototype.hasOwnProperty.call(new Object(t),e)},Ae.mixin=(t,e)=>{for(const r in e)Ae.hasOwn(e,r)&&(t[r]=e[r])};var Ee,Oe,je,Te=Ee={};function Pe(){throw new Error("setTimeout has not been defined")}function xe(){throw new Error("clearTimeout has not been defined")}function ke(t){if(Oe===setTimeout)return setTimeout(t,0);if((Oe===Pe||!Oe)&&setTimeout)return Oe=setTimeout,setTimeout(t,0);try{return Oe(t,0)}catch(ir){try{return Oe.call(null,t,0)}catch(ir){return Oe.call(this,t,0)}}}!function(){try{Oe="function"==typeof setTimeout?setTimeout:Pe}catch(ir){Oe=Pe}try{je="function"==typeof clearTimeout?clearTimeout:xe}catch(ir){je=xe}}();var Re,Ie=[],Ce=!1,Ne=-1;function Fe(){Ce&&Re&&(Ce=!1,Re.length?Ie=Re.concat(Ie):Ne=-1,Ie.length&&De())}function De(){if(!Ce){var t=ke(Fe);Ce=!0;for(var e=Ie.length;e;){for(Re=Ie,Ie=[];++Ne<e;)Re&&Re[Ne].run();Ne=-1,e=Ie.length}Re=null,Ce=!1,function(t){if(je===clearTimeout)return clearTimeout(t);if((je===xe||!je)&&clearTimeout)return je=clearTimeout,clearTimeout(t);try{je(t)}catch(ir){try{return je.call(null,t)}catch(ir){return je.call(this,t)}}}(t)}}function Ue(t,e){this.fun=t,this.array=e}function Me(){}Te.nextTick=function(t){var e=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)e[r-1]=arguments[r];Ie.push(new Ue(t,e)),1!==Ie.length||Ce||ke(De)},Ue.prototype.run=function(){this.fun.apply(null,this.array)},Te.title="browser",Te.browser=!0,Te.env={},Te.argv=[],Te.version="",Te.versions={},Te.on=Me,Te.addListener=Me,Te.once=Me,Te.off=Me,Te.removeListener=Me,Te.removeAllListeners=Me,Te.emit=Me,Te.prependListener=Me,Te.prependOnceListener=Me,Te.listeners=function(t){return[]},Te.binding=function(t){throw new Error("process.binding is not supported")},Te.cwd=function(){return"/"},Te.chdir=function(t){throw new Error("process.chdir is not supported")},Te.umask=function(){return 0};var Le={};(function(t){(function(){const{isObject:e,hasOwn:r}=Ae;function o(){}Le=o,o.prototype.clearTimeout=function(){return clearTimeout(this._timer),clearTimeout(this._responseTimeoutTimer),clearTimeout(this._uploadTimeoutTimer),delete this._timer,delete this._responseTimeoutTimer,delete this._uploadTimeoutTimer,this},o.prototype.parse=function(t){return this._parser=t,this},o.prototype.responseType=function(t){return this._responseType=t,this},o.prototype.serialize=function(t){return this._serializer=t,this},o.prototype.timeout=function(t){if(!t||"object"!=typeof t)return this._timeout=t,this._responseTimeout=0,this._uploadTimeout=0,this;for(const e in t)if(r(t,e))switch(e){case"deadline":this._timeout=t.deadline;break;case"response":this._responseTimeout=t.response;break;case"upload":this._uploadTimeout=t.upload;break;default:console.warn("Unknown timeout option",e)}return this},o.prototype.retry=function(t,e){return 0!==arguments.length&&!0!==t||(t=1),t<=0&&(t=0),this._maxRetries=t,this._retries=0,this._retryCallback=e,this};const n=new Set(["ETIMEDOUT","ECONNRESET","EADDRINUSE","ECONNREFUSED","EPIPE","ENOTFOUND","ENETUNREACH","EAI_AGAIN"]),i=new Set([408,413,429,500,502,503,504,521,522,524]);o.prototype._shouldRetry=function(t,e){if(!this._maxRetries||this._retries++>=this._maxRetries)return!1;if(this._retryCallback)try{const r=this._retryCallback(t,e);if(!0===r)return!0;if(!1===r)return!1}catch(r){console.error(r)}if(e&&e.status&&i.has(e.status))return!0;if(t){if(t.code&&n.has(t.code))return!0;if(t.timeout&&"ECONNABORTED"===t.code)return!0;if(t.crossDomain)return!0}return!1},o.prototype._retry=function(){return this.clearTimeout(),this.req&&(this.req=null,this.req=this.request()),this._aborted=!1,this.timedout=!1,this.timedoutError=null,this._end()},o.prototype.then=function(t,e){if(!this._fullfilledPromise){const t=this;this._endCalled&&console.warn("Warning: superagent request was sent twice, because both .end() and .then() were called. Never call .end() if you use promises"),this._fullfilledPromise=new Promise((e,r)=>{t.on("abort",()=>{if(this._maxRetries&&this._maxRetries>this._retries)return;if(this.timedout&&this.timedoutError)return void r(this.timedoutError);const t=new Error("Aborted");t.code="ABORTED",t.status=this.status,t.method=this.method,t.url=this.url,r(t)}),t.end((t,o)=>{t?r(t):e(o)})})}return this._fullfilledPromise.then(t,e)},o.prototype.catch=function(t){return this.then(void 0,t)},o.prototype.use=function(t){return t(this),this},o.prototype.ok=function(t){if("function"!=typeof t)throw new Error("Callback required");return this._okCallback=t,this},o.prototype._isResponseOK=function(t){return!!t&&(this._okCallback?this._okCallback(t):t.status>=200&&t.status<300)},o.prototype.get=function(t){return this._header[t.toLowerCase()]},o.prototype.getHeader=o.prototype.get,o.prototype.set=function(t,o){if(e(t)){for(const e in t)r(t,e)&&this.set(e,t[e]);return this}return this._header[t.toLowerCase()]=o,this.header[t]=o,this},o.prototype.unset=function(t){return delete this._header[t.toLowerCase()],delete this.header[t],this},o.prototype.field=function(t,o,n){if(null==t)throw new Error(".field(name, val) name can not be empty");if(this._data)throw new Error(".field() can't be used if .send() is used. Please use only .send() or only .field() & .attach()");if(e(t)){for(const e in t)r(t,e)&&this.field(e,t[e]);return this}if(Array.isArray(o)){for(const e in o)r(o,e)&&this.field(t,o[e]);return this}if(null==o)throw new Error(".field(name, val) val can not be empty");return"boolean"==typeof o&&(o=String(o)),n?this._getFormData().append(t,o,n):this._getFormData().append(t,o),this},o.prototype.abort=function(){if(this._aborted)return this;if(this._aborted=!0,this.xhr&&this.xhr.abort(),this.req){if(Y.gte(t.version,"v13.0.0")&&Y.lt(t.version,"v14.0.0"))throw new Error("Superagent does not work in v13 properly with abort() due to Node.js core changes");this.req.abort()}return this.clearTimeout(),this.emit("abort"),this},o.prototype._auth=function(t,e,r,o){switch(r.type){case"basic":this.set("Authorization","Basic "+o(`${t}:${e}`));break;case"auto":this.username=t,this.password=e;break;case"bearer":this.set("Authorization","Bearer "+t)}return this},o.prototype.withCredentials=function(t){return void 0===t&&(t=!0),this._withCredentials=t,this},o.prototype.redirects=function(t){return this._maxRedirects=t,this},o.prototype.maxResponseSize=function(t){if("number"!=typeof t)throw new TypeError("Invalid argument");return this._maxResponseSize=t,this},o.prototype.toJSON=function(){return{method:this.method,url:this.url,data:this._data,headers:this._header}},o.prototype.send=function(t){const o=e(t);let n=this._header["content-type"];if(this._formData)throw new Error(".send() can't be used if .attach() or .field() is used. Please use only .send() or only .field() & .attach()");if(o&&!this._data)Array.isArray(t)?this._data=[]:this._isHost(t)||(this._data={});else if(t&&this._data&&this._isHost(this._data))throw new Error("Can't merge these send calls");if(o&&e(this._data))for(const e in t){if("bigint"==typeof t[e]&&!t[e].toJSON)throw new Error("Cannot serialize BigInt value to json");r(t,e)&&(this._data[e]=t[e])}else{if("bigint"==typeof t)throw new Error("Cannot send value of type BigInt");"string"==typeof t?(n||this.type("form"),(n=this._header["content-type"])&&(n=n.toLowerCase().trim()),this._data="application/x-www-form-urlencoded"===n?this._data?`${this._data}&${t}`:t:(this._data||"")+t):this._data=t}return!o||this._isHost(t)||n||this.type("json"),this},o.prototype.sortQuery=function(t){return this._sort=void 0===t||t,this},o.prototype._finalizeQueryString=function(){const t=this._query.join("&");if(t&&(this.url+=(this.url.includes("?")?"&":"?")+t),this._query.length=0,this._sort){const t=this.url.indexOf("?");if(t>=0){const e=this.url.slice(t+1).split("&");"function"==typeof this._sort?e.sort(this._sort):e.sort(),this.url=this.url.slice(0,t)+"?"+e.join("&")}}},o.prototype._appendQueryString=()=>{console.warn("Unsupported")},o.prototype._timeoutError=function(t,e,r){if(this._aborted)return;const o=new Error(t+e+"ms exceeded");o.timeout=e,o.code="ECONNABORTED",o.errno=r,this.timedout=!0,this.timedoutError=o,this.abort(),this.callback(o)},o.prototype._setTimeouts=function(){const t=this;this._timeout&&!this._timer&&(this._timer=setTimeout(()=>{t._timeoutError("Timeout of ",t._timeout,"ETIME")},this._timeout)),this._responseTimeout&&!this._responseTimeoutTimer&&(this._responseTimeoutTimer=setTimeout(()=>{t._timeoutError("Response timeout of ",t._responseTimeout,"ETIMEDOUT")},this._responseTimeout))}}).call(this)}).call(this,Ee);var qe;function Be(){}qe=Be,Be.prototype.get=function(t){return this.header[t.toLowerCase()]},Be.prototype._setHeaderProperties=function(t){const e=t["content-type"]||"";this.type=Ae.type(e);const r=Ae.params(e);for(const n in r)Object.prototype.hasOwnProperty.call(r,n)&&(this[n]=r[n]);this.links={};try{t.link&&(this.links=Ae.parseLinks(t.link))}catch(o){}},Be.prototype._setStatusProperties=function(t){const e=Math.trunc(t/100);this.statusCode=t,this.status=this.statusCode,this.statusType=e,this.info=1===e,this.ok=2===e,this.redirect=3===e,this.clientError=4===e,this.serverError=5===e,this.error=(4===e||5===e)&&this.toError(),this.created=201===t,this.accepted=202===t,this.noContent=204===t,this.badRequest=400===t,this.unauthorized=401===t,this.notAcceptable=406===t,this.forbidden=403===t,this.notFound=404===t,this.unprocessableEntity=422===t};var We={};function $e(){this._defaults=[]}for(const ar of["use","on","once","set","query","type","accept","auth","withCredentials","sortQuery","retry","ok","redirects","timeout","buffer","serialize","parse","ca","key","pfx","cert","disableTLSCerts"])$e.prototype[ar]=function(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];return this._defaults.push({fn:ar,args:e}),this};$e.prototype._setDefaults=function(t){for(const e of this._defaults)t[e.fn](...e.args)},We=$e;var He={};let ze;"undefined"!=typeof window?ze=window:"undefined"==typeof self?(console.warn("Using browser-only version of superagent in non-browser environment"),ze=this):ze=self;const{isObject:Ge,mixin:Je,hasOwn:Ve}=Ae;function Qe(){}const Xe=He=He=function(t,e){return"function"==typeof e?new He.Request("GET",t).end(e):1===arguments.length?new He.Request("GET",t):new He.Request(t,e)};He.Request=or,Xe.getXHR=()=>{if(ze.XMLHttpRequest)return new ze.XMLHttpRequest;throw new Error("Browser-only version of superagent could not find XHR")};const Ke="".trim?t=>t.trim():t=>t.replace(/(^\s*|\s*$)/g,"");function Ye(t){if(!Ge(t))return t;const e=[];for(const r in t)Ve(t,r)&&Ze(e,r,t[r]);return e.join("&")}function Ze(t,e,r){if(void 0!==r)if(null!==r)if(Array.isArray(r))for(const o of r)Ze(t,e,o);else if(Ge(r))for(const o in r)Ve(r,o)&&Ze(t,`${e}[${o}]`,r[o]);else t.push(encodeURI(e)+"="+encodeURIComponent(r));else t.push(encodeURI(e))}function tr(t){const e={},r=t.split("&");let o,n;for(let i=0,a=r.length;i<a;++i)-1===(n=(o=r[i]).indexOf("="))?e[decodeURIComponent(o)]="":e[decodeURIComponent(o.slice(0,n))]=decodeURIComponent(o.slice(n+1));return e}function er(t){return/[/+]json($|[^-\w])/i.test(t)}function rr(t){this.req=t,this.xhr=this.req.xhr,this.text="HEAD"!==this.req.method&&(""===this.xhr.responseType||"text"===this.xhr.responseType)||void 0===this.xhr.responseType?this.xhr.responseText:null,this.statusText=this.req.xhr.statusText;let{status:e}=this.xhr;1223===e&&(e=204),this._setStatusProperties(e),this.headers=function(t){const e=t.split(/\r?\n/),r={};let o,n,i,a;for(let s=0,u=e.length;s<u;++s)-1!==(o=(n=e[s]).indexOf(":"))&&(i=n.slice(0,o).toLowerCase(),a=Ke(n.slice(o+1)),r[i]=a);return r}(this.xhr.getAllResponseHeaders()),this.header=this.headers,this.header["content-type"]=this.xhr.getResponseHeader("content-type"),this._setHeaderProperties(this.header),null===this.text&&t._responseType?this.body=this.xhr.response:this.body="HEAD"===this.req.method?null:this._parseBody(this.text?this.text:this.xhr.response)}function or(t,e){const r=this;this._query=this._query||[],this.method=t,this.url=e,this.header={},this._header={},this.on("end",()=>{let t,e=null,o=null;try{o=new rr(r)}catch(n){return(e=new Error("Parser is unable to parse the response")).parse=!0,e.original=n,r.xhr?(e.rawResponse=void 0===r.xhr.responseType?r.xhr.responseText:r.xhr.response,e.status=r.xhr.status?r.xhr.status:null,e.statusCode=e.status):(e.rawResponse=null,e.status=null),r.callback(e)}r.emit("response",o);try{r._isResponseOK(o)||(t=new Error(o.statusText||o.text||"Unsuccessful HTTP response"))}catch(n){t=n}t?(t.original=e,t.response=o,t.status=t.status||o.status,r.callback(t,o)):r.callback(null,o)})}Xe.serializeObject=Ye,Xe.parseString=tr,Xe.types={html:"text/html",json:"application/json",xml:"text/xml",urlencoded:"application/x-www-form-urlencoded",form:"application/x-www-form-urlencoded","form-data":"application/x-www-form-urlencoded"},Xe.serialize={"application/x-www-form-urlencoded":Se.stringify,"application/json":r},Xe.parse={"application/x-www-form-urlencoded":tr,"application/json":JSON.parse},Je(rr.prototype,qe.prototype),rr.prototype._parseBody=function(t){let e=Xe.parse[this.type];return this.req._parser?this.req._parser(this,t):(!e&&er(this.type)&&(e=Xe.parse["application/json"]),e&&t&&(t.length>0||t instanceof Object)?e(t):null)},rr.prototype.toError=function(){const{req:t}=this,{method:e}=t,{url:r}=t,o=`cannot ${e} ${r} (${this.status})`,n=new Error(o);return n.status=this.status,n.method=e,n.url=r,n},Xe.Response=rr,t(or.prototype),Je(or.prototype,Le.prototype),or.prototype.type=function(t){return this.set("Content-Type",Xe.types[t]||t),this},or.prototype.accept=function(t){return this.set("Accept",Xe.types[t]||t),this},or.prototype.auth=function(t,e,r){1===arguments.length&&(e=""),"object"==typeof e&&null!==e&&(r=e,e=""),r||(r={type:"function"==typeof btoa?"basic":"auto"});const o=r.encoder?r.encoder:t=>{if("function"==typeof btoa)return btoa(t);throw new Error("Cannot use basic auth, btoa is not a function")};return this._auth(t,e,r,o)},or.prototype.query=function(t){return"string"!=typeof t&&(t=Ye(t)),t&&this._query.push(t),this},or.prototype.attach=function(t,e,r){if(e){if(this._data)throw new Error("superagent can't mix .send() and .attach()");this._getFormData().append(t,e,r||e.name)}return this},or.prototype._getFormData=function(){return this._formData||(this._formData=new ze.FormData),this._formData},or.prototype.callback=function(t,e){if(this._shouldRetry(t,e))return this._retry();const r=this._callback;this.clearTimeout(),t&&(this._maxRetries&&(t.retries=this._retries-1),this.emit("error",t)),r(t,e)},or.prototype.crossDomainError=function(){const t=new Error("Request has been terminated\nPossible causes: the network is offline, Origin is not allowed by Access-Control-Allow-Origin, the page is being unloaded, etc.");t.crossDomain=!0,t.status=this.status,t.method=this.method,t.url=this.url,this.callback(t)},or.prototype.agent=function(){return console.warn("This is not supported in browser version of superagent"),this},or.prototype.ca=or.prototype.agent,or.prototype.buffer=or.prototype.ca,or.prototype.write=()=>{throw new Error("Streaming is not supported in browser version of superagent")},or.prototype.pipe=or.prototype.write,or.prototype._isHost=function(t){return t&&"object"==typeof t&&!Array.isArray(t)&&"[object Object]"!==Object.prototype.toString.call(t)},or.prototype.end=function(t){this._endCalled&&console.warn("Warning: .end() was called twice. This is not supported in superagent"),this._endCalled=!0,this._callback=t||Qe,this._finalizeQueryString(),this._end()},or.prototype._setUploadTimeout=function(){const t=this;this._uploadTimeout&&!this._uploadTimeoutTimer&&(this._uploadTimeoutTimer=setTimeout(()=>{t._timeoutError("Upload timeout of ",t._uploadTimeout,"ETIMEDOUT")},this._uploadTimeout))},or.prototype._end=function(){if(this._aborted)return this.callback(new Error("The request has been aborted even before .end() was called"));const t=this;this.xhr=Xe.getXHR();const{xhr:e}=this;let r=this._formData||this._data;this._setTimeouts(),e.addEventListener("readystatechange",()=>{const{readyState:r}=e;if(r>=2&&t._responseTimeoutTimer&&clearTimeout(t._responseTimeoutTimer),4!==r)return;let o;try{o=e.status}catch(n){o=0}if(!o){if(t.timedout||t._aborted)return;return t.crossDomainError()}t.emit("end")});const o=(e,r)=>{r.total>0&&(r.percent=r.loaded/r.total*100,100===r.percent&&clearTimeout(t._uploadTimeoutTimer)),r.direction=e,t.emit("progress",r)};if(this.hasListeners("progress"))try{e.addEventListener("progress",o.bind(null,"download")),e.upload&&e.upload.addEventListener("progress",o.bind(null,"upload"))}catch(n){}e.upload&&this._setUploadTimeout();try{this.username&&this.password?e.open(this.method,this.url,!0,this.username,this.password):e.open(this.method,this.url,!0)}catch(n){return this.callback(n)}if(this._withCredentials&&(e.withCredentials=!0),!this._formData&&"GET"!==this.method&&"HEAD"!==this.method&&"string"!=typeof r&&!this._isHost(r)){const t=this._header["content-type"];let e=this._serializer||Xe.serialize[t?t.split(";")[0]:""];!e&&er(t)&&(e=Xe.serialize["application/json"]),e&&(r=e(r))}for(const i in this.header)null!==this.header[i]&&Ve(this.header,i)&&e.setRequestHeader(i,this.header[i]);this._responseType&&(e.responseType=this._responseType),this.emit("request",this),e.send(void 0===r?null:r)},Xe.agent=()=>new We;for(const ar of["GET","POST","OPTIONS","PATCH","PUT","DELETE"])We.prototype[ar.toLowerCase()]=function(t,e){const r=new Xe.Request(ar,t);return this._setDefaults(r),e&&r.end(e),r};function nr(t,e,r){const o=Xe("DELETE",t);return"function"==typeof e&&(r=e,e=null),e&&o.send(e),r&&o.end(r),o}return We.prototype.del=We.prototype.delete,Xe.get=(t,e,r)=>{const o=Xe("GET",t);return"function"==typeof e&&(r=e,e=null),e&&o.query(e),r&&o.end(r),o},Xe.head=(t,e,r)=>{const o=Xe("HEAD",t);return"function"==typeof e&&(r=e,e=null),e&&o.query(e),r&&o.end(r),o},Xe.options=(t,e,r)=>{const o=Xe("OPTIONS",t);return"function"==typeof e&&(r=e,e=null),e&&o.send(e),r&&o.end(r),o},Xe.del=nr,Xe.delete=nr,Xe.patch=(t,e,r)=>{const o=Xe("PATCH",t);return"function"==typeof e&&(r=e,e=null),e&&o.send(e),r&&o.end(r),o},Xe.post=(t,e,r)=>{const o=Xe("POST",t);return"function"==typeof e&&(r=e,e=null),e&&o.send(e),r&&o.end(r),o},Xe.put=(t,e,r)=>{const o=Xe("PUT",t);return"function"==typeof e&&(r=e,e=null),e&&o.send(e),r&&o.end(r),o},He}));