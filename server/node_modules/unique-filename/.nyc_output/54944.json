{"./index.js": {"path": "./index.js", "s": {"1": 1, "2": 1, "3": 1, "4": 6}, "b": {"1": [4, 2]}, "f": {"1": 6}, "fnMap": {"1": {"name": "(anonymous_1)", "line": 6, "loc": {"start": {"line": 6, "column": 17}, "end": {"line": 6, "column": 51}}}}, "statementMap": {"1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 26}}, "2": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 39}}, "3": {"start": {"line": 6, "column": 0}, "end": {"line": 8, "column": 1}}, "4": {"start": {"line": 7, "column": 2}, "end": {"line": 7, "column": 77}}}, "branchMap": {"1": {"line": 7, "type": "cond-expr", "locations": [{"start": {"line": 7, "column": 39}, "end": {"line": 7, "column": 51}}, {"start": {"line": 7, "column": 54}, "end": {"line": 7, "column": 56}}]}}}}