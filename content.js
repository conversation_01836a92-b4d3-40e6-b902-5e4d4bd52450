// 题库管理系统内容脚本
class QuestionExtractor {
    constructor() {
        this.sidebarInjected = false;
        this.init();
    }

    init() {
        console.log('题库管理系统内容脚本已加载');
        this.injectSidebar();
        this.setupMessageListener();
    }

    // 注入侧边栏
    async injectSidebar() {
        if (this.sidebarInjected) return;

        try {
            // 创建侧边栏iframe
            const sidebarFrame = document.createElement('iframe');
            sidebarFrame.id = 'question-bank-sidebar';
            sidebarFrame.src = chrome.runtime.getURL('sidebar.html');
            sidebarFrame.style.cssText = `
                position: fixed !important;
                top: 0 !important;
                right: 0 !important;
                width: 20vw !important;
                min-width: 320px !important;
                max-width: 400px !important;
                height: 100vh !important;
                border: none !important;
                z-index: ********** !important;
                background: white !important;
                box-shadow: -2px 0 10px rgba(0,0,0,0.1) !important;
                display: block !important;
            `;

            document.body.appendChild(sidebarFrame);
            this.sidebarInjected = true;
            console.log('侧边栏已注入');
        } catch (error) {
            console.error('注入侧边栏失败:', error);
        }
    }

    // 设置消息监听器
    setupMessageListener() {
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            if (request.action === 'extractQuestions') {
                this.extractQuestions().then(questions => {
                    sendResponse({ questions });
                }).catch(error => {
                    sendResponse({ error: error.message });
                });
                return true; // 异步响应
            }
        });
    }

    // 提取题目数据
    async extractQuestions() {
        return new Promise((resolve) => {
            // 等待页面加载完成
            setTimeout(() => {
                const questions = [];

                // 尝试多种选择器来适配不同的题库网站
                const selectors = [
                    '.question-list .item',
                    '.ques-item',
                    '.question',
                    '.JV_list .item',
                    '.question-item',
                    '.exam-item',
                    '.test-item',
                    '[class*="question"]',
                    '[class*="ques"]'
                ];

                let questionElements = [];
                for (const selector of selectors) {
                    questionElements = document.querySelectorAll(selector);
                    if (questionElements.length > 0) {
                        console.log(`使用选择器 ${selector} 找到 ${questionElements.length} 个题目元素`);
                        break;
                    }
                }

                if (questionElements.length === 0) {
                    // 如果没有找到标准的题目元素，尝试通过文本特征识别
                    questionElements = this.findQuestionsByText();
                }

                questionElements.forEach((element, index) => {
                    const questionData = this.parseQuestionElement(element, index);
                    if (questionData.content) {
                        questions.push(questionData);
                    }
                });

                console.log(`提取到 ${questions.length} 道题目`);
                resolve(questions);
            }, 2000);
        });
    }

    // 通过文本特征查找题目
    findQuestionsByText() {
        const elements = [];
        const textNodes = document.evaluate(
            "//text()[contains(., '题') or contains(., '问') or contains(., '选择') or contains(., '填空') or contains(., '判断')]",
            document,
            null,
            XPathResult.UNORDERED_NODE_SNAPSHOT_TYPE,
            null
        );

        for (let i = 0; i < textNodes.snapshotLength; i++) {
            const textNode = textNodes.snapshotItem(i);
            const parentElement = textNode.parentElement;

            if (parentElement && this.isQuestionElement(parentElement)) {
                elements.push(parentElement);
            }
        }

        return elements;
    }

    // 判断是否为题目元素
    isQuestionElement(element) {
        const text = element.textContent.trim();
        const questionPatterns = [
            /^\d+[\.、]\s*/, // 以数字开头
            /[题问]\s*[:：]/, // 包含"题"或"问"
            /[选择填空判断].*题/, // 题型关键词
            /[ABCD][\.、)]/, // 选项标识
        ];

        return questionPatterns.some(pattern => pattern.test(text)) && text.length > 10;
    }

    // 解析题目元素
    parseQuestionElement(element, index) {
        const questionData = {
            id: index + 1,
            grade: this.extractGrade(element),
            subject: this.extractSubject(element),
            difficulty: this.extractDifficulty(element),
            usage_count: this.extractUsageCount(element),
            content: this.extractContent(element),
            source_url: window.location.href
        };

        return questionData;
    }

    // 提取年级信息
    extractGrade(element) {
        const gradePatterns = [
            /[高初][一二三]/g,
            /[高初]\d/g,
            /高中|初中/g
        ];

        const text = element.textContent || '';
        for (const pattern of gradePatterns) {
            const match = text.match(pattern);
            if (match) {
                return match[0];
            }
        }

        // 从页面URL或标题中提取
        const pageText = document.title + ' ' + window.location.href;
        for (const pattern of gradePatterns) {
            const match = pageText.match(pattern);
            if (match) {
                return match[0];
            }
        }

        return '未知年级';
    }

    // 提取学科信息
    extractSubject(element) {
        const subjects = ['数学', '物理', '化学', '生物', '语文', '英语', '历史', '地理', '政治'];
        const text = element.textContent || '';

        for (const subject of subjects) {
            if (text.includes(subject)) {
                return subject;
            }
        }

        // 从页面标题中提取
        const pageText = document.title + ' ' + window.location.href;
        for (const subject of subjects) {
            if (pageText.includes(subject)) {
                return subject;
            }
        }

        return '未知学科';
    }

    // 提取难度信息
    extractDifficulty(element) {
        const difficulties = ['简单', '中等', '困难', '易', '中', '难'];
        const text = element.textContent || '';

        for (const difficulty of difficulties) {
            if (text.includes(difficulty)) {
                return difficulty === '易' ? '简单' : difficulty === '难' ? '困难' : difficulty;
            }
        }

        return '中等'; // 默认难度
    }

    // 提取组卷次数
    extractUsageCount(element) {
        const text = element.textContent || '';
        const usageMatch = text.match(/组卷\s*(\d+)/);
        if (usageMatch) {
            return parseInt(usageMatch[1]);
        }

        const countMatch = text.match(/使用\s*(\d+)/);
        if (countMatch) {
            return parseInt(countMatch[1]);
        }

        return Math.floor(Math.random() * 10); // 随机生成0-9的使用次数
    }

    // 提取题目内容
    extractContent(element) {
        // 尝试多种方式提取题目内容
        const contentSelectors = [
            '.ques-title',
            '.title',
            '.question-title',
            '.q-title',
            'h3',
            'h4',
            '.content',
            '.question-content'
        ];

        for (const selector of contentSelectors) {
            const contentEl = element.querySelector(selector);
            if (contentEl && contentEl.textContent.trim()) {
                return this.cleanContent(contentEl.textContent.trim());
            }
        }

        // 如果没有找到特定的内容元素，使用整个元素的文本
        const fullText = element.textContent.trim();
        if (fullText.length > 10) {
            return this.cleanContent(fullText);
        }

        return '';
    }

    // 清理题目内容
    cleanContent(content) {
        return content
            .replace(/^\d+[\.、]\s*/, '') // 移除题号
            .replace(/\s+/g, ' ') // 合并多个空格
            .replace(/[\r\n]+/g, ' ') // 替换换行符
            .trim()
            .substring(0, 500); // 限制长度
    }
}

// 初始化
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        new QuestionExtractor();
    });
} else {
    new QuestionExtractor();
}
