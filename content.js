document.addEventListener('DOMContentLoaded', function() {
  console.log('内容脚本已加载');
  
  // 检查是否登录
  function checkLoginStatus() {
    // 检查页面上是否有登录相关的元素
    const loginElement = document.querySelector('.login-item, .top-login-item');
    return loginElement && loginElement.textContent.includes('登录');
  }
  
  // 提取题目数据
  function extractQuestions() {
    let questions = [];
    // 由于题目可能是动态加载的，等待一段时间以确保内容加载完成
    return new Promise((resolve) => {
      setTimeout(() => {
        if (checkLoginStatus()) {
          resolve({ error: '未登录，请先登录题库网。' });
          return;
        }
        
        // 尝试不同的选择器来定位题目元素
        const questionElements = document.querySelectorAll('.question-list .item, .ques-item, .question, .JV_list .item');
        
        questionElements.forEach((element, index) => {
          const title = element.querySelector('.ques-title, .title, h3, .q-title')?.textContent.trim() || '';
          const options = Array.from(element.querySelectorAll('.options .option, .opt, li, .q-options li')).map(opt => opt.textContent.trim());
          const answer = element.querySelector('.answer, .ans, .q-answer')?.textContent.trim() || '';
          
          questions.push({
            id: index + 1,
            title: title,
            options: options,
            answer: answer
          });
        });
        
        resolve(questions);
      }, 3000); // 等待3秒以确保动态内容加载
    });
  }
  
  // 向弹出页面发送数据
  chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
    if (request.action === 'getQuestions') {
      extractQuestions().then(result => {
        if (result.error) {
          sendResponse({ error: result.error });
        } else {
          sendResponse({ questions: result });
        }
      });
      return true; // 异步响应
    }
  });
});
