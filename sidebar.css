/* 侧边栏容器 */
#sidebar-container {
    position: fixed;
    top: 0;
    right: 0;
    width: 20vw;
    min-width: 320px;
    max-width: 400px;
    height: 100vh;
    background: #ffffff;
    border-left: 2px solid #e0e0e0;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
    z-index: 10000;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

/* 标签栏 */
.tab-bar {
    display: flex;
    background: #f5f5f5;
    border-bottom: 1px solid #e0e0e0;
}

.tab-button {
    flex: 1;
    padding: 12px 8px;
    border: none;
    background: transparent;
    cursor: pointer;
    font-size: 14px;
    color: #666;
    transition: all 0.3s ease;
}

.tab-button.active {
    background: #ffffff;
    color: #333;
    border-bottom: 2px solid #007bff;
}

.tab-button:hover {
    background: #e9ecef;
}

/* 按钮栏 */
.button-bar {
    display: flex;
    padding: 10px;
    gap: 8px;
    background: #fafafa;
    border-bottom: 1px solid #e0e0e0;
}

.action-btn {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #007bff;
    background: #007bff;
    color: white;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
}

.action-btn:hover {
    background: #0056b3;
    border-color: #0056b3;
}

.action-btn:disabled {
    background: #6c757d;
    border-color: #6c757d;
    cursor: not-allowed;
}

/* 标签页内容 */
.tab-content {
    display: none;
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.tab-content.active {
    display: flex;
}

/* 状态栏 */
.status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 10px;
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
    font-size: 12px;
}

.small-btn {
    padding: 4px 8px;
    border: 1px solid #6c757d;
    background: #6c757d;
    color: white;
    border-radius: 3px;
    cursor: pointer;
    font-size: 11px;
    margin-left: 5px;
}

.small-btn:hover {
    background: #5a6268;
}

/* 题目列表 */
.questions-list {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
}

.question-card {
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    margin-bottom: 10px;
    background: #ffffff;
    transition: all 0.3s ease;
}

.question-card:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.question-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
    border-radius: 6px 6px 0 0;
}

.question-tags {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
    flex: 1;
}

.tag {
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    color: white;
}

.tag.grade { background: #28a745; }
.tag.subject { background: #007bff; }
.tag.difficulty { background: #ffc107; color: #333; }
.tag.usage { background: #6c757d; }

.question-checkbox {
    margin-left: 10px;
}

.question-content {
    padding: 12px;
    line-height: 1.4;
    color: #333;
}

/* 搜索区域 */
.search-section {
    padding: 10px;
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
}

.search-row {
    display: flex;
    gap: 8px;
    margin-bottom: 8px;
}

.search-row:last-child {
    margin-bottom: 0;
}

.filter-select, .search-input, .sort-select {
    flex: 1;
    padding: 6px 8px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 12px;
}

.search-input {
    flex: 2;
}

/* 结果区域 */
.results-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 10px;
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
    font-size: 12px;
}

/* 分页 */
.pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: #f8f9fa;
    border-top: 1px solid #e0e0e0;
}

.page-btn {
    padding: 6px 12px;
    border: 1px solid #007bff;
    background: #007bff;
    color: white;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.page-btn:disabled {
    background: #6c757d;
    border-color: #6c757d;
    cursor: not-allowed;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d;
}

/* 模态框 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10001;
    display: flex;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 400px;
    max-height: 80%;
    overflow: hidden;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
}

.modal-header h3 {
    margin: 0;
    font-size: 16px;
}

.close-btn {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #6c757d;
}

.modal-body {
    padding: 20px;
}

.setting-item {
    margin-bottom: 15px;
}

.setting-item label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.setting-item input[type="text"] {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
}

.setting-item input[type="checkbox"] {
    margin-right: 8px;
}

.checkbox-label {
    font-size: 14px;
    color: #333;
}

.modal-footer {
    display: flex;
    gap: 10px;
    padding: 15px 20px;
    background: #f8f9fa;
    border-top: 1px solid #e0e0e0;
}

/* 加载指示器 */
.loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    background: rgba(255, 255, 255, 0.9);
    padding: 20px;
    border-radius: 8px;
    z-index: 10002;
}

.spinner {
    width: 30px;
    height: 30px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 消息提示 */
.message {
    position: absolute;
    top: 10px;
    left: 10px;
    right: 10px;
    padding: 10px 15px;
    border-radius: 4px;
    font-size: 13px;
    z-index: 10003;
    animation: slideDown 0.3s ease;
}

.message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.message.info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

@keyframes slideDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* 滚动条样式 */
.questions-list::-webkit-scrollbar {
    width: 6px;
}

.questions-list::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.questions-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.questions-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
