// 题库管理系统后台脚本
class BackgroundService {
    constructor() {
        this.init();
    }

    init() {
        this.setupInstallListener();
        this.setupActionListener();
        this.setupMessageListener();
        console.log('题库管理系统后台服务已启动');
    }

    // 安装监听器
    setupInstallListener() {
        chrome.runtime.onInstalled.addListener((details) => {
            console.log('题库管理系统已安装', details);

            // 设置默认配置
            chrome.storage.sync.set({
                apiUrl: 'http://localhost:3000',
                autoSave: false
            });
        });
    }

    // 点击插件图标监听器
    setupActionListener() {
        chrome.action.onClicked.addListener(async (tab) => {
            try {
                // 检查是否已经注入了内容脚本
                await chrome.scripting.executeScript({
                    target: { tabId: tab.id },
                    func: () => {
                        // 检查侧边栏是否已存在
                        const existingSidebar = document.getElementById('question-bank-sidebar');
                        if (existingSidebar) {
                            // 切换显示/隐藏
                            const isHidden = existingSidebar.style.display === 'none';
                            existingSidebar.style.display = isHidden ? 'block' : 'none';
                            return { action: 'toggle', visible: isHidden };
                        }
                        return { action: 'inject' };
                    }
                });

                console.log('侧边栏状态已切换');
            } catch (error) {
                console.error('切换侧边栏失败:', error);
            }
        });
    }

    // 消息监听器
    setupMessageListener() {
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            if (request.action === 'getTabInfo') {
                // 返回当前标签页信息
                chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
                    if (tabs[0]) {
                        sendResponse({
                            url: tabs[0].url,
                            title: tabs[0].title,
                            id: tabs[0].id
                        });
                    }
                });
                return true;
            }

            if (request.action === 'getVersion') {
                sendResponse({ version: chrome.runtime.getManifest().version });
            }
        });
    }
}

// 初始化后台服务
new BackgroundService();
