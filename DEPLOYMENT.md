# 题库管理系统部署和使用指南

## 系统概述

题库管理系统包含两个主要组件：
1. **Chrome插件** - 前端界面，用于提取和管理题目
2. **Node.js后端服务** - API服务器，处理数据存储和查询

## 部署步骤

### 1. 后端服务部署

#### 安装依赖
```bash
cd server
npm install
```

#### 启动服务
```bash
# 开发模式（推荐）
npm run dev

# 生产模式
npm start
```

#### 验证服务
访问 http://localhost:3000/health 应该返回：
```json
{
  "success": true,
  "message": "服务器运行正常",
  "timestamp": "2025-07-08T05:03:38.055Z",
  "version": "1.0.0"
}
```

### 2. Chrome插件安装

#### 开发者模式安装
1. 打开Chrome浏览器
2. 访问 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择项目根目录（包含manifest.json的文件夹）
6. 插件安装成功后会显示在扩展程序列表中

#### 验证安装
- 插件图标应该出现在Chrome工具栏
- 点击插件图标应该在页面右侧显示侧边栏

## 使用指南

### 基本使用流程

#### 1. 配置API地址
- 点击Chrome插件图标打开侧边栏
- 点击"设置"按钮
- 确认API地址为 `http://localhost:3000`
- 点击"测试连接"验证连接
- 点击"保存设置"

#### 2. 提取题目数据
- 打开包含题目的网页（如test-page.html）
- 点击插件图标打开侧边栏
- 确保在"提取页面"标签下
- 点击"读取"按钮提取题目
- 查看提取到的题目列表
- 选择需要保存的题目（默认全选）
- 点击"保存"按钮将题目保存到数据库

#### 3. 查看和搜索题目
- 切换到"展示页面"标签
- 使用筛选条件：年级、学科、难度
- 输入关键词搜索题目内容
- 点击"搜索"按钮查看结果
- 使用排序功能按组卷次数或创建时间排序

### 高级功能

#### 自动保存
- 在设置中开启"自动保存"
- 读取题目后会自动保存到数据库

#### 批量操作
- 使用"全选"和"清空"按钮快速选择题目
- 支持批量保存多道题目

#### 分页浏览
- 展示页面支持分页浏览
- 每页显示10道题目
- 使用"上一页"/"下一页"按钮导航

## 测试说明

### 使用测试页面
1. 确保后端服务已启动
2. 在浏览器中打开 `test-page.html`
3. 点击Chrome插件图标
4. 测试提取、保存、搜索等功能

### API测试
```bash
# 健康检查
curl http://localhost:3000/health

# 查询题目
curl http://localhost:3000/api/questions

# 保存题目
curl -X POST http://localhost:3000/api/questions \
  -H "Content-Type: application/json" \
  -d '{"grade":"高一","subject":"数学","difficulty":"中等","content":"测试题目","usage_count":5}'

# 获取统计信息
curl http://localhost:3000/api/statistics
```

## 故障排除

### 常见问题

#### 1. 插件无法加载
- 检查manifest.json语法是否正确
- 确保所有文件路径正确
- 查看Chrome扩展程序页面的错误信息

#### 2. 侧边栏不显示
- 检查content.js是否正确注入
- 查看浏览器控制台错误信息
- 确认页面URL匹配content_scripts的matches规则

#### 3. 无法连接后端API
- 确认后端服务是否启动
- 检查API地址配置是否正确
- 查看CORS设置是否允许Chrome插件访问

#### 4. 题目提取失败
- 检查页面结构是否包含题目元素
- 查看content.js的选择器是否匹配
- 在浏览器控制台查看提取日志

#### 5. 数据库错误
- 检查SQLite数据库文件权限
- 查看服务器日志错误信息
- 确认数据格式是否正确

### 调试技巧

#### Chrome插件调试
1. 打开Chrome开发者工具
2. 查看Console标签页的错误信息
3. 在Extensions页面点击插件的"检查视图"
4. 使用断点调试JavaScript代码

#### 后端服务调试
1. 查看服务器控制台输出
2. 使用Postman或curl测试API接口
3. 检查数据库文件是否正确创建
4. 查看网络请求和响应

## 性能优化

### 前端优化
- 减少DOM查询次数
- 使用事件委托处理大量元素
- 优化CSS选择器性能
- 实现虚拟滚动处理大量数据

### 后端优化
- 添加数据库索引
- 实现查询结果缓存
- 优化SQL查询语句
- 添加请求限流

## 安全考虑

### 数据安全
- 验证所有输入数据
- 防止SQL注入攻击
- 限制文件上传大小
- 实现访问频率限制

### 隐私保护
- 不收集用户个人信息
- 本地存储敏感配置
- 使用HTTPS传输数据
- 定期清理临时数据

## 扩展功能

### 可能的改进
1. 支持更多题库网站
2. 添加题目分类管理
3. 实现题目导出功能
4. 添加用户权限管理
5. 支持题目编辑功能
6. 实现数据备份恢复
7. 添加统计图表显示
8. 支持多语言界面
