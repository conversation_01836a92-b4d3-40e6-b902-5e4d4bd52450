// 题库管理系统侧边栏脚本
class QuestionBankSidebar {
    constructor() {
        this.apiUrl = 'http://localhost:3000';
        this.currentTab = 'extract';
        this.extractedQuestions = [];
        this.displayedQuestions = [];
        this.currentPage = 1;
        this.pageSize = 10;
        this.totalPages = 1;
        
        this.init();
    }

    // 初始化
    async init() {
        await this.loadSettings();
        this.bindEvents();
        this.showTab('extract');
        console.log('题库管理系统侧边栏已初始化');
    }

    // 加载设置
    async loadSettings() {
        try {
            const result = await chrome.storage.sync.get(['apiUrl', 'autoSave']);
            if (result.apiUrl) {
                this.apiUrl = result.apiUrl;
                document.getElementById('api-url').value = result.apiUrl;
            }
            if (result.autoSave) {
                document.getElementById('auto-save').checked = result.autoSave;
            }
        } catch (error) {
            console.error('加载设置失败:', error);
        }
    }

    // 保存设置
    async saveSettings() {
        try {
            const apiUrl = document.getElementById('api-url').value.trim();
            const autoSave = document.getElementById('auto-save').checked;
            
            if (!apiUrl) {
                this.showMessage('请输入API地址', 'error');
                return;
            }

            await chrome.storage.sync.set({ apiUrl, autoSave });
            this.apiUrl = apiUrl;
            
            this.showMessage('设置保存成功', 'success');
            this.hideModal('settings-modal');
        } catch (error) {
            console.error('保存设置失败:', error);
            this.showMessage('保存设置失败', 'error');
        }
    }

    // 绑定事件
    bindEvents() {
        // 标签切换
        document.querySelectorAll('.tab-button').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tab = e.target.dataset.tab;
                this.showTab(tab);
            });
        });

        // 功能按钮
        document.getElementById('read-btn').addEventListener('click', () => this.readQuestions());
        document.getElementById('save-btn').addEventListener('click', () => this.saveQuestions());
        document.getElementById('settings-btn').addEventListener('click', () => this.showModal('settings-modal'));

        // 设置相关
        document.getElementById('close-settings').addEventListener('click', () => this.hideModal('settings-modal'));
        document.getElementById('save-settings').addEventListener('click', () => this.saveSettings());
        document.getElementById('test-connection').addEventListener('click', () => this.testConnection());

        // 提取页面功能
        document.getElementById('select-all-btn').addEventListener('click', () => this.selectAllQuestions());
        document.getElementById('clear-selection-btn').addEventListener('click', () => this.clearSelection());

        // 展示页面功能
        document.getElementById('search-btn').addEventListener('click', () => this.searchQuestions());
        document.getElementById('refresh-btn').addEventListener('click', () => this.refreshQuestions());
        
        // 分页
        document.getElementById('prev-page').addEventListener('click', () => this.prevPage());
        document.getElementById('next-page').addEventListener('click', () => this.nextPage());

        // 排序
        document.getElementById('sort-order').addEventListener('change', () => this.searchQuestions());

        // 回车搜索
        document.getElementById('keyword-search').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.searchQuestions();
            }
        });
    }

    // 显示标签页
    showTab(tabName) {
        // 更新标签按钮状态
        document.querySelectorAll('.tab-button').forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.tab === tabName) {
                btn.classList.add('active');
            }
        });

        // 更新内容显示
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`${tabName}-tab`).classList.add('active');

        this.currentTab = tabName;

        // 如果切换到展示页面，自动加载数据
        if (tabName === 'display') {
            this.searchQuestions();
        }
    }

    // 显示模态框
    showModal(modalId) {
        document.getElementById(modalId).style.display = 'flex';
    }

    // 隐藏模态框
    hideModal(modalId) {
        document.getElementById(modalId).style.display = 'none';
    }

    // 显示加载状态
    showLoading(show = true) {
        document.getElementById('loading').style.display = show ? 'flex' : 'none';
    }

    // 显示消息
    showMessage(text, type = 'info') {
        const messageEl = document.getElementById('message');
        messageEl.textContent = text;
        messageEl.className = `message ${type}`;
        messageEl.style.display = 'block';

        setTimeout(() => {
            messageEl.style.display = 'none';
        }, 3000);
    }

    // 测试连接
    async testConnection() {
        this.showLoading(true);
        try {
            const response = await fetch(`${this.apiUrl}/health`);
            const data = await response.json();
            
            if (data.success) {
                this.showMessage('连接测试成功', 'success');
            } else {
                this.showMessage('连接测试失败', 'error');
            }
        } catch (error) {
            console.error('连接测试失败:', error);
            this.showMessage('连接测试失败: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    // 读取题目数据
    async readQuestions() {
        this.showLoading(true);
        try {
            // 向content script发送消息获取题目数据
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            const response = await chrome.tabs.sendMessage(tab.id, { 
                action: 'extractQuestions' 
            });

            if (response && response.questions) {
                this.extractedQuestions = response.questions;
                this.renderExtractedQuestions();
                this.showMessage(`成功读取 ${response.questions.length} 道题目`, 'success');
                
                // 检查自动保存设置
                const settings = await chrome.storage.sync.get(['autoSave']);
                if (settings.autoSave) {
                    setTimeout(() => this.saveQuestions(), 1000);
                }
            } else {
                this.showMessage(response?.error || '未找到题目数据', 'error');
            }
        } catch (error) {
            console.error('读取题目失败:', error);
            this.showMessage('读取题目失败: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    // 渲染提取的题目
    renderExtractedQuestions() {
        const container = document.getElementById('questions-container');
        const countEl = document.getElementById('question-count');
        const selectAllBtn = document.getElementById('select-all-btn');
        const clearBtn = document.getElementById('clear-selection-btn');

        if (this.extractedQuestions.length === 0) {
            container.innerHTML = '<div class="empty-state"><p>未找到题目数据</p></div>';
            countEl.textContent = '未读取题目';
            selectAllBtn.style.display = 'none';
            clearBtn.style.display = 'none';
            return;
        }

        countEl.textContent = `共 ${this.extractedQuestions.length} 道题目`;
        selectAllBtn.style.display = 'inline-block';
        clearBtn.style.display = 'inline-block';

        const questionsHtml = this.extractedQuestions.map((question, index) => `
            <div class="question-card">
                <div class="question-header">
                    <div class="question-tags">
                        <span class="tag grade">${question.grade || '未知年级'}</span>
                        <span class="tag subject">${question.subject || '未知学科'}</span>
                        <span class="tag difficulty">${question.difficulty || '未知难度'}</span>
                        <span class="tag usage">组卷 ${question.usage_count || 0} 次</span>
                    </div>
                    <input type="checkbox" class="question-checkbox" data-index="${index}" checked>
                </div>
                <div class="question-content">
                    ${question.content || '题目内容为空'}
                </div>
            </div>
        `).join('');

        container.innerHTML = questionsHtml;
    }

    // 全选题目
    selectAllQuestions() {
        document.querySelectorAll('.question-checkbox').forEach(checkbox => {
            checkbox.checked = true;
        });
    }

    // 清空选择
    clearSelection() {
        document.querySelectorAll('.question-checkbox').forEach(checkbox => {
            checkbox.checked = false;
        });
    }

    // 保存题目到后端
    async saveQuestions() {
        const selectedQuestions = this.getSelectedQuestions();
        
        if (selectedQuestions.length === 0) {
            this.showMessage('请选择要保存的题目', 'error');
            return;
        }

        this.showLoading(true);
        try {
            const response = await fetch(`${this.apiUrl}/api/questions`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(selectedQuestions)
            });

            const data = await response.json();
            
            if (data.success) {
                this.showMessage(`成功保存 ${selectedQuestions.length} 道题目`, 'success');
                // 清空已保存的题目选择
                this.clearSelection();
            } else {
                this.showMessage('保存失败: ' + data.message, 'error');
            }
        } catch (error) {
            console.error('保存题目失败:', error);
            this.showMessage('保存失败: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    // 获取选中的题目
    getSelectedQuestions() {
        const selected = [];
        document.querySelectorAll('.question-checkbox:checked').forEach(checkbox => {
            const index = parseInt(checkbox.dataset.index);
            if (this.extractedQuestions[index]) {
                selected.push(this.extractedQuestions[index]);
            }
        });
        return selected;
    }

    // 搜索题目
    async searchQuestions() {
        this.showLoading(true);
        try {
            const grade = document.getElementById('grade-filter').value;
            const subject = document.getElementById('subject-filter').value;
            const difficulty = document.getElementById('difficulty-filter').value;
            const keyword = document.getElementById('keyword-search').value.trim();
            const sortOrder = document.getElementById('sort-order').value;

            // 解析排序参数
            const [orderBy, order] = sortOrder.split('_');

            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.pageSize,
                orderBy,
                order: order.toUpperCase()
            });

            if (grade) params.append('grade', grade);
            if (subject) params.append('subject', subject);
            if (difficulty) params.append('difficulty', difficulty);
            if (keyword) params.append('keyword', keyword);

            const response = await fetch(`${this.apiUrl}/api/questions?${params}`);
            const data = await response.json();

            if (data.success) {
                this.displayedQuestions = data.data;
                this.renderDisplayedQuestions();
                this.updatePagination(data.pagination);

                const count = data.data.length;
                document.getElementById('results-count').textContent = `共 ${count} 道题目`;
            } else {
                this.showMessage('搜索失败: ' + data.message, 'error');
            }
        } catch (error) {
            console.error('搜索题目失败:', error);
            this.showMessage('搜索失败: ' + error.message, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    // 刷新题目列表
    async refreshQuestions() {
        this.currentPage = 1;
        await this.searchQuestions();
    }

    // 渲染展示的题目
    renderDisplayedQuestions() {
        const container = document.getElementById('results-container');

        if (this.displayedQuestions.length === 0) {
            container.innerHTML = '<div class="empty-state"><p>未找到符合条件的题目</p></div>';
            return;
        }

        const questionsHtml = this.displayedQuestions.map(question => `
            <div class="question-card">
                <div class="question-header">
                    <div class="question-tags">
                        <span class="tag grade">${question.grade}</span>
                        <span class="tag subject">${question.subject}</span>
                        <span class="tag difficulty">${question.difficulty}</span>
                        <span class="tag usage">组卷 ${question.usage_count} 次</span>
                    </div>
                    <small style="color: #6c757d;">ID: ${question.id}</small>
                </div>
                <div class="question-content">
                    ${question.content}
                </div>
                <div style="padding: 8px 12px; background: #f8f9fa; border-top: 1px solid #e0e0e0; font-size: 11px; color: #6c757d;">
                    创建时间: ${new Date(question.created_at).toLocaleString()}
                    ${question.source_url ? `| 来源: ${question.source_url}` : ''}
                </div>
            </div>
        `).join('');

        container.innerHTML = questionsHtml;
    }

    // 更新分页信息
    updatePagination(pagination) {
        const paginationEl = document.getElementById('pagination');
        const prevBtn = document.getElementById('prev-page');
        const nextBtn = document.getElementById('next-page');
        const pageInfo = document.getElementById('page-info');

        if (pagination && pagination.total > this.pageSize) {
            paginationEl.style.display = 'flex';

            this.totalPages = Math.ceil(pagination.total / this.pageSize);
            pageInfo.textContent = `第 ${this.currentPage} 页 / 共 ${this.totalPages} 页`;

            prevBtn.disabled = this.currentPage <= 1;
            nextBtn.disabled = this.currentPage >= this.totalPages;
        } else {
            paginationEl.style.display = 'none';
        }
    }

    // 上一页
    async prevPage() {
        if (this.currentPage > 1) {
            this.currentPage--;
            await this.searchQuestions();
        }
    }

    // 下一页
    async nextPage() {
        if (this.currentPage < this.totalPages) {
            this.currentPage++;
            await this.searchQuestions();
        }
    }
}

// 当DOM加载完成后初始化侧边栏
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        new QuestionBankSidebar();
    });
} else {
    new QuestionBankSidebar();
}
