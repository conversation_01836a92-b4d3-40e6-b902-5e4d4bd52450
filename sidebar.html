<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>题库管理系统</title>
    <link rel="stylesheet" href="sidebar.css">
</head>
<body>
    <div id="sidebar-container">
        <!-- 标签栏 -->
        <div class="tab-bar">
            <button class="tab-button active" data-tab="extract">提取页面</button>
            <button class="tab-button" data-tab="display">展示页面</button>
        </div>

        <!-- 按钮栏 -->
        <div class="button-bar">
            <button id="read-btn" class="action-btn">读取</button>
            <button id="save-btn" class="action-btn">保存</button>
            <button id="settings-btn" class="action-btn">设置</button>
        </div>

        <!-- 提取页面内容 -->
        <div id="extract-tab" class="tab-content active">
            <div class="status-bar">
                <span id="question-count">未读取题目</span>
                <button id="select-all-btn" class="small-btn" style="display: none;">全选</button>
                <button id="clear-selection-btn" class="small-btn" style="display: none;">清空</button>
            </div>
            
            <div id="questions-container" class="questions-list">
                <div class="empty-state">
                    <p>点击"读取"按钮开始提取题目数据</p>
                </div>
            </div>
        </div>

        <!-- 展示页面内容 -->
        <div id="display-tab" class="tab-content">
            <!-- 搜索筛选区域 -->
            <div class="search-section">
                <div class="search-row">
                    <select id="grade-filter" class="filter-select">
                        <option value="">全部年级</option>
                        <option value="高一">高一</option>
                        <option value="高二">高二</option>
                        <option value="高三">高三</option>
                        <option value="初一">初一</option>
                        <option value="初二">初二</option>
                        <option value="初三">初三</option>
                    </select>
                    <select id="subject-filter" class="filter-select">
                        <option value="">全部学科</option>
                        <option value="数学">数学</option>
                        <option value="物理">物理</option>
                        <option value="化学">化学</option>
                        <option value="生物">生物</option>
                        <option value="语文">语文</option>
                        <option value="英语">英语</option>
                        <option value="历史">历史</option>
                        <option value="地理">地理</option>
                        <option value="政治">政治</option>
                    </select>
                </div>
                <div class="search-row">
                    <select id="difficulty-filter" class="filter-select">
                        <option value="">全部难度</option>
                        <option value="简单">简单</option>
                        <option value="中等">中等</option>
                        <option value="困难">困难</option>
                    </select>
                    <input type="text" id="keyword-search" class="search-input" placeholder="搜索题目内容...">
                </div>
                <div class="search-row">
                    <button id="search-btn" class="action-btn">搜索</button>
                    <button id="refresh-btn" class="action-btn">刷新</button>
                </div>
            </div>

            <!-- 结果显示区域 -->
            <div class="results-section">
                <div class="results-header">
                    <span id="results-count">共0道题目</span>
                    <select id="sort-order" class="sort-select">
                        <option value="usage_count_desc">组卷次数↓</option>
                        <option value="usage_count_asc">组卷次数↑</option>
                        <option value="created_at_desc">创建时间↓</option>
                        <option value="created_at_asc">创建时间↑</option>
                    </select>
                </div>
                
                <div id="results-container" class="questions-list">
                    <div class="empty-state">
                        <p>点击"搜索"按钮查看题目数据</p>
                    </div>
                </div>
                
                <!-- 分页控制 -->
                <div class="pagination" id="pagination" style="display: none;">
                    <button id="prev-page" class="page-btn">上一页</button>
                    <span id="page-info">第1页 / 共1页</span>
                    <button id="next-page" class="page-btn">下一页</button>
                </div>
            </div>
        </div>

        <!-- 设置弹窗 -->
        <div id="settings-modal" class="modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>设置</h3>
                    <button id="close-settings" class="close-btn">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="setting-item">
                        <label for="api-url">后端API地址:</label>
                        <input type="text" id="api-url" placeholder="http://localhost:3000" value="http://localhost:3000">
                    </div>
                    <div class="setting-item">
                        <label for="auto-save">自动保存:</label>
                        <input type="checkbox" id="auto-save">
                        <span class="checkbox-label">读取后自动保存题目</span>
                    </div>
                </div>
                <div class="modal-footer">
                    <button id="save-settings" class="action-btn">保存设置</button>
                    <button id="test-connection" class="action-btn">测试连接</button>
                </div>
            </div>
        </div>

        <!-- 加载指示器 -->
        <div id="loading" class="loading" style="display: none;">
            <div class="spinner"></div>
            <span>处理中...</span>
        </div>

        <!-- 消息提示 -->
        <div id="message" class="message" style="display: none;"></div>
    </div>

    <script src="sidebar.js"></script>
</body>
</html>
